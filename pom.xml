<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ximalaya.ad</groupId>
    <artifactId>ad-rdp</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.2</version>
    </parent>

    <modules>
        <module>ad-rdp-main</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>artifactory</id>
            <name>ximalaya-releases</name>
            <url>http://artifactory.ximalaya.com/artifactory/ximalaya-releases/</url>
        </repository>
        <snapshotRepository>
            <id>artifactory</id>
            <name>ximalaya-snapshots</name>
            <url>http://artifactory.ximalaya.com/artifactory/ximalaya-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <envcfg.dir>dev</envcfg.dir>
                <!-- 有profile差异的依赖写在这里 -->
                <ad.commons.verison>1.1.0-SNAPSHOT</ad.commons.verison>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <envcfg.dir>test</envcfg.dir>
                <ad.commons.verison>1.1.0-SNAPSHOT</ad.commons.verison>
            </properties>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <envcfg.dir>product</envcfg.dir>
                <ad.commons.verison>1.6.56</ad.commons.verison>
            </properties>
        </profile>
    </profiles>

</project>