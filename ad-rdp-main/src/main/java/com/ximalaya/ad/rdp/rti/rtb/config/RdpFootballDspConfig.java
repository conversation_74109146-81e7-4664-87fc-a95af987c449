package com.ximalaya.ad.rdp.rti.rtb.config;

import com.ximalaya.ad.rdp.rti.rtb.config.dsp.SurgeRtbConfig;
import com.ximalaya.football.client.spring.annotation.FootballConfig;
import com.ximalaya.football.client.spring.annotation.FootballField;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * RdpFootballDspConfig
 *
 * @version 2025/04/27 16:55
 **/
@FootballConfig("rdpDspConfig")
@Component
@Data
public class RdpFootballDspConfig {
    @FootballField(name = "surgeRtbConfig", defaultValue = "{}")
    private SurgeRtbConfig surgeRtbConfig;
}
