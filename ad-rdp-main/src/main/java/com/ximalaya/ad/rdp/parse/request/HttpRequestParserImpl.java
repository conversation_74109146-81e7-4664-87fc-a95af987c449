package com.ximalaya.ad.rdp.parse.request;

import com.ximalaya.ad.rdp.metrics.RetCode;
import com.ximalaya.ad.rdp.proto.RdpProto;
import com.ximalaya.ad.rdp.service.SequenceIdService;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class HttpRequestParserImpl implements HttpRequestParser{
    @Autowired
    private ApplicationContext applicationContext;

    @Resource(name = "sequenceIdService")
    private SequenceIdService sequenceIdService;

    @Override
    public RdpRequest parse(HttpServletRequest request) {
        RdpRequest rdpRequest = applicationContext.getBean(RdpRequest.class);
        try {
            rdpRequest.setBidRequest(RdpProto.BidRequest.parseFrom(request.getInputStream()));
            rdpRequest.setRequestIdNumber(sequenceIdService.nextId());
        } catch (IOException e) {
            rdpRequest.setRetCode(RetCode.REQ_PARSE_ERR);
            rdpRequest.setRetMsg("parse http request to protobuf bidRequest error!" + e.getMessage());
        }
        return rdpRequest;
    }
}
