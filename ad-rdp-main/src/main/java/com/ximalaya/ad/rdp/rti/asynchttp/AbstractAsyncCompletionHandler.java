package com.ximalaya.ad.rdp.rti.asynchttp;

import com.ximalaya.ad.common.monitor.AdPrometheusService;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.service.impl.LoggingServiceImpl;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.asynchttpclient.AsyncCompletionHandler;
import org.asynchttpclient.Response;

import java.util.List;

/**
 * 异步响应处理器的抽象基类
 * @param <T> 响应解析后的类型
 **/
@Slf4j
public abstract class AbstractAsyncCompletionHandler<T> extends AsyncCompletionHandler<T> {

    protected long requestIdNumber;
    boolean canLog;
    protected List<RequestBidStat> requestBidStats;

    private static final Counter DSP_RESPONSE_SIZE = Counter.build()
                                                            .name("dsp_response_size")
                                                            .help("dsp response size")
                                                            .labelNames("dsp", "temp_id")
                                                            .create();

    static {
        AdPrometheusService.getInstance().registerCollector(DSP_RESPONSE_SIZE);
    }

    AbstractAsyncCompletionHandler(long requestIdNumber, boolean canLog, List<RequestBidStat> requestBidStats) {
        this.requestIdNumber = requestIdNumber;
        this.canLog = canLog;
        this.requestBidStats = requestBidStats;
    }

    @Override
    public void onThrowable(Throwable t) {
        LoggingServiceImpl.logCanForDebug(getClass(), new LogMessageBuilder(t.getMessage()), canLog, requestIdNumber);
    }

    @Override
    public T onCompleted(Response response) throws Exception {
        logSetCookie(response);
        DSP_RESPONSE_SIZE.labels(String.valueOf(requestBidStats.getFirst().getDspId()), String.valueOf(requestBidStats.getFirst().getImpToTemplateMap().values().stream()
                                                                                                                      .findFirst()
                                                                                                                      .orElse(null)))
                         .inc(response.getResponseBody().length());
        return parseResponse(response);
    }

    public abstract T parseResponse(Response response);

    private void logSetCookie(Response response) {
        String cookie = response.getHeader("Set-Cookie");
        if (StringUtils.isNotBlank(cookie) && cookie.length() > 1024) {
            log.info("url: {} set big cookie: {}", response.getUri().toUrl(), cookie);
        }
    }
}
