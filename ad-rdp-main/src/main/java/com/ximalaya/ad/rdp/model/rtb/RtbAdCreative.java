package com.ximalaya.ad.rdp.model.rtb;

import com.ximalaya.ad.rdp.model.AdCreative;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * RtbAdCreative RTB物料
 *
 * @version 2025/04/29 11:01
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class RtbAdCreative extends AdCreative {
    private List<String> impTrackers;
    private List<String> winNoticeUrls;
    private List<String> lossNoticeUrls;
    private String landingPage;
    private BigDecimal price;
    private String impId;
    private String templateId;
}
