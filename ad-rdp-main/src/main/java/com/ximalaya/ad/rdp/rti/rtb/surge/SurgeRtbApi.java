package com.ximalaya.ad.rdp.rti.rtb.surge;

import com.google.common.collect.ImmutableMap;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.constant.*;
import com.ximalaya.ad.rdp.model.*;
import com.ximalaya.ad.rdp.model.rtb.RtbAdCreative;
import com.ximalaya.ad.rdp.model.rtb.RtbCreativeBidInfo;
import com.ximalaya.ad.rdp.rti.rtb.TemplateMaterialProcessor;
import com.ximalaya.ad.rdp.rti.rtb.asynchttp.RdpAsyncHttpClient;
import com.ximalaya.ad.rdp.rti.rtb.asynchttp.ThirdDspFuture;
import com.ximalaya.ad.rdp.rti.rtb.config.RdpFootballDspConfig;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.SurgeRtbConfig;
import com.ximalaya.ad.rdp.proto.RdpProto;
import com.ximalaya.ad.rdp.rti.rtb.RtbApi;
import com.ximalaya.ad.rdp.proto.SurgeBidding;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.stat.*;
import com.ximalaya.ad.rdp.vo.request.Context;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.asynchttpclient.RequestBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Component
public class SurgeRtbApi implements RtbApi {

    @Autowired
    private RdpAsyncHttpClient<SurgeBidRequest, SurgeBidding.BidResponse> rdpAsyncHttpClient;
    @Autowired
    private LoggingService loggingService;
    @Autowired
    private RdpFootballDspConfig rdpFootballDspConfig;
    @Autowired
    private TemplateMaterialProcessor templateMaterialProcessor;

    protected static final Map<Integer, Integer> DEVICE_TYPE = ImmutableMap.<Integer, Integer>builder()
                                                                           .put(DeviceType.MOBILE.getCode(), 4)
                                                                           .put(DeviceType.TABLET.getCode(), 5)
                                                                           .build()
            ;

    private static final Map<Integer, Integer> CONNECTION_TYPE = ImmutableMap.<Integer, Integer>builder()
                                                                             .put(NetworkType.NET_2G.getCode(), 4)
                                                                             .put(NetworkType.NET_3G.getCode(), 5)
                                                                             .put(NetworkType.NET_4G.getCode(), 6)
                                                                             .put(NetworkType.WIFI.getCode(), 2)
                                                                             .put(NetworkType.UNKNOWN.getCode(), 0)
                                                                             .build()
            ;
    private static final Map<Integer, Integer> AD_TYPE = ImmutableMap.<Integer, Integer>builder()
                                                                     .put(AdType.FEED.getCode(), 1)
                                                                     .put(AdType.SPLASH.getCode(), 2)
                                                                     .put(AdType.BANNER.getCode(), 3)
                                                                     .put(AdType.REWARDED_VIDEO.getCode(), 9)
                                                                     .build()
            ;

    private static final Map<Integer, Integer> CARRIER_MAP = ImmutableMap.<Integer, Integer>builder()
            .put(Carrier.CHINA_MOBILE.getCode(), 1)
            .put(Carrier.CHINA_UNICOM.getCode(), 2)
            .put(Carrier.CHINA_TELECOM.getCode(), 3)
            .put(Carrier.UNKNOWN.getCode(), 0)
            .build();

    private static final String SETTLE_PRICE_MACRO = "${AUCTION_PRICE}";


    private static final int SURGE_ACCOUNT_ID = 1001;
    @Override
    public int accountId() {
        return SURGE_ACCOUNT_ID;
    }

    // 定义 materialType 到提取方法的映射
    private static final Map<MaterialType, java.util.function.Function<SurgeBidding.BidResponse.Bid.Adm, String>> SURGE_MATERIAL_EXTRACTOR_MAP = new HashMap<>();
    static {
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.H_IMAGE, adm -> 
            adm.getImageCount() > 0 && adm.getImage(0) != null ? adm.getImage(0).getUrl() : StringUtils.EMPTY);
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.V_IMAGE, adm -> 
            adm.getImageCount() > 0 && adm.getImage(0) != null ? adm.getImage(0).getUrl() : StringUtils.EMPTY);
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.H_VIDEO, adm -> 
            adm.getVideo() != null ? adm.getVideo().getVideoUrl() : StringUtils.EMPTY);
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.V_VIDEO, adm -> 
            adm.getVideo() != null ? adm.getVideo().getVideoUrl() : StringUtils.EMPTY);
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.TITLE, SurgeBidding.BidResponse.Bid.Adm::getTitle);
        SURGE_MATERIAL_EXTRACTOR_MAP.put(MaterialType.DESC, SurgeBidding.BidResponse.Bid.Adm::getDesc);
    }

    @Override
    public List<CreativeBidInfo<? extends AdCreative>> hit(RdpRequest request, List<AdGroup> adGroups, Context context) {
        if (CollectionUtils.isEmpty(adGroups)) {
            return Collections.emptyList();
        }
        // 暂不支持批量请求
        AdGroup adGroup = adGroups.getFirst();
        RequestBidStat requestBidStat = context.getOrCreateRequestBidStat(adGroup.getAccount());

        SurgeRtbConfig surgeRtbConfig = rdpFootballDspConfig.getSurgeRtbConfig();

        // 异步请求
        Optional<SurgeBidRequestData> requestOptional = buildRequest(request, surgeRtbConfig);
        requestOptional.ifPresent(bidRequestResult -> {
            requestBidStat.addAllTemplateToImpMappings(bidRequestResult.getImpToTemplateMap());
        });
        ThirdDspFuture<SurgeBidding.BidResponse> thirdDspFuture = requestOptional.map(bidRequestData -> 
            getResponse(bidRequestData.getBidRequest(), requestBidStat, request, surgeRtbConfig, adGroup)).orElse(null);
        if (thirdDspFuture == null) {
            return Collections.emptyList();
        }

        // 等待响应，使用配置的超时时间
        long timeout = surgeRtbConfig.getTimeout(request);
        SurgeBidding.BidResponse response = thirdDspFuture.get(timeout, TimeUnit.MILLISECONDS);
        
        // 处理响应
        if (response == null) {
            log.warn(LogMessageBuilder.MESSAGE_HOLDER, new LogMessageBuilder("surge_rtb 没有返回结果").addParameter("rdpRequest", request));
            return Collections.emptyList();
        }
        List<RtbCreativeBidInfo> creativeBidInfos = fillCreative(response, requestBidStat, adGroup);
        return new ArrayList<>(creativeBidInfos);
    }

    /**
     * 封装SurgeBidRequest和模板ID到imp ID的映射
     */
    @Data
    @AllArgsConstructor
    private static class SurgeBidRequestData {
        private final SurgeBidRequest bidRequest;
        private final Map<String, String> impToTemplateMap;
    }

    private Optional<SurgeBidRequestData> buildRequest(RdpRequest rdpRequest, SurgeRtbConfig surgeRtbConfig) {
        SurgeBidRequest bidRequest = new SurgeBidRequest();
        bidRequest.setId(UUID.randomUUID().toString());
        bidRequest.setApi_version(surgeRtbConfig.getApiVersion());
        bidRequest.setTest(0);

        List<RdpProto.BidRequest.Imp> rdpImpList = rdpRequest.getValidImps();
        if (CollectionUtils.isEmpty(rdpImpList)) {
            return Optional.empty();
        }
        List<SurgeBidRequest.Imp> surgeImps = new ArrayList<>();
        Map<String, String> impToTemplateMap = new HashMap<>();
        
        for (RdpProto.BidRequest.Imp rdpImp : rdpImpList) {
            Pair<List<SurgeBidRequest.Imp>, Map<String, String>> impressionResult = buildImpression(rdpImp, surgeRtbConfig);
            surgeImps.addAll(impressionResult.getLeft());
            
            // 合并所有impId到templateId的映射
            impressionResult.getRight().forEach((impId, templateId) -> {
                impToTemplateMap.put(impId, templateId);
            });
        }
        if (surgeImps.isEmpty()) {
            log.warn(LogMessageBuilder.MESSAGE_HOLDER, new LogMessageBuilder("surge_rtb 没有有效的imp").addParameter("rdpRequest", rdpRequest));
            return Optional.empty();
        }
        // Imp 去重
        List<SurgeBidRequest.Imp> uniqueImps = deduplicateImps(surgeImps);
        
        // 同步更新 impId到templateId的映射
        syncTemplateToImpMapping(impToTemplateMap, uniqueImps);
        
        bidRequest.setImp(uniqueImps);

        Optional<SurgeBidRequest.CustomizedUserTag> customizedUserTag = buildCustomizedUserTag(rdpRequest, surgeRtbConfig);
        customizedUserTag.ifPresent(bidRequest::setCustomized_user_tag);

        Optional<RdpProto.BidRequest.App> rdpApp = rdpRequest.getApp();
        if (rdpApp.isPresent()) {
            SurgeBidRequest.App app = buildApp(rdpApp.get());
            bidRequest.setApp(app);
        } else {
            return Optional.empty();
        }

        Optional<RdpProto.BidRequest.Device> rdpDevice = rdpRequest.getDevice();
        if (rdpDevice.isPresent()) {
            SurgeBidRequest.Device device = buildDevice(rdpDevice.get());
            bidRequest.setDevice(device);
        } else {
            return Optional.empty();
        }

        return Optional.of(new SurgeBidRequestData(bidRequest, impToTemplateMap));
    }

    private List<RtbCreativeBidInfo> fillCreative(SurgeBidding.BidResponse response, RequestBidStat requestBidStat, AdGroup adGroup) {
        loggingService.logForDebug(getClass(), new LogMessageBuilder("surge_rtb response").addParameter("detail", response));
        List<RtbCreativeBidInfo> infoList = new ArrayList<>();
        
        if (response == null || response.getSeatBidCount() == 0) {
            loggingService.logForDebug(getClass(), new LogMessageBuilder("invalid surge_rtb response")
                    .addParameter("detail", response)
                    .addParameter("nbr", response.getNbr()));
            requestBidStat.setResult(ResultCode.DSP_REFUSE);
            return infoList;
        }

        List<SurgeBidding.BidResponse.Bid> bidList = response.getSeatBid(0).getBidList();
        if (CollectionUtils.isEmpty(bidList)) {
            requestBidStat.setResult(ResultCode.DSP_REFUSE);
            return infoList;
        }

        // 获取对应info
        for (SurgeBidding.BidResponse.Bid bid : bidList) {
            RtbCreativeBidInfo rtbCreativeBidInfo = new RtbCreativeBidInfo();
            rtbCreativeBidInfo.setAdGroup(adGroup);
            // TODO: rpc?查询adAccount
            // rtbCreativeBidInfo.setAccount(adGroup.getAccount());
            SurgeBidding.BidResponse.Bid.Adm adm = bid.getAdm();
            if (Objects.isNull(adm)) {
                continue;
            }

            Optional<RtbAdCreative> adCreativeOpt = getRtbAdCreative(adm, bid, requestBidStat);
            if (adCreativeOpt.isEmpty()) {
                continue;
            }
            RtbAdCreative adCreative = adCreativeOpt.get();
            Map<String, String> materials = templateMaterialProcessor.fillMaterialsByTemplate(adCreative.getTemplateId(), adm, SURGE_MATERIAL_EXTRACTOR_MAP);
            if (materials.isEmpty()) {
                continue;
            }
            adCreative.setMaterials(materials);

            rtbCreativeBidInfo.setAdCreative(adCreative);
            infoList.add(rtbCreativeBidInfo);

            BidStat bidStat = requestBidStat.getOrCreateBidStat(bid.getImpId());
            bidStat.setResult(ResultCode.DSP_ACCEPT);
            bidStat.setImpId(bid.getImpId());
            bidStat.setCreativeId(bid.getCreativeId());
            bidStat.setTemplateId(adCreative.getTemplateId());
        }
        requestBidStat.updateResultFromBidStats();
        
        return infoList;
    }

    private ThirdDspFuture<SurgeBidding.BidResponse> getResponse(SurgeBidRequest bidRequest, RequestBidStat requestBidStat, RdpRequest rdpRequest, SurgeRtbConfig surgeRtbConfig, AdGroup adGroup) {
        var requestBuilder = rdpAsyncHttpClient.post(surgeRtbConfig.getRequestUrl(), surgeRtbConfig)
                .addHeader("Content-Type", "application/json");
        return rdpAsyncHttpClient.getJsonToProtoResponse(rdpRequest, requestBuilder, bidRequest, SurgeBidding.BidResponse::parseFrom,
                List.of(requestBidStat));
    }

    // 封装创意基础信息
    private Optional<RtbAdCreative> getRtbAdCreative(SurgeBidding.BidResponse.Bid.Adm adm, SurgeBidding.BidResponse.Bid bid, RequestBidStat requestBidStat) {
        RtbAdCreative adCreative = new RtbAdCreative();
        adCreative.setDpLink(adm.getDeepLink());
        adCreative.setUlk(adm.getUniversalLink());
        adCreative.setLandingPage(adm.getLandingSite());

        adCreative.setClickUrl(bid.getClkTrackersList().getFirst());    // NOTE: 与AdCreative保持一致，只取第一个点击监测链接
        adCreative.setImpTrackers(bid.getImpTrackersList());
        adCreative.setWinNoticeUrls(Collections.singletonList(bid.getNurl()));
        adCreative.setLossNoticeUrls(Collections.singletonList(bid.getLurl()));
        adCreative.setPrice(BigDecimal.valueOf(bid.getPrice()));

        String impId = adCreative.getImpId();
        adCreative.setImpId(impId);

        String templateId = requestBidStat.getTemplateIdByImpId(impId);
        if (StringUtils.isEmpty(templateId)) {
            log.warn("templateId is empty for impId: {}", impId);
            return Optional.empty();
        }
        adCreative.setTemplateId(templateId);
        return Optional.of(adCreative);
    }

    private Pair<List<SurgeBidRequest.Imp>, Map<String, String>> buildImpression(RdpProto.BidRequest.Imp rdpImp, SurgeRtbConfig surgeRtbConfig) {
        List<SurgeBidRequest.Imp> impList = new ArrayList<>();
        Map<String, String> impToTemplateMap = new HashMap<>();

        for (RdpProto.BidRequest.Imp.Asset rdpAsset : rdpImp.getAssetsList()) {
            // 根据模板装配 Imp
            List<SurgeRtbConfig.SurgeTemplateConfig> templateConfigs = surgeRtbConfig.getTemplateConfig(rdpAsset.getTemplateId());
            for (SurgeRtbConfig.SurgeTemplateConfig templateConfig : templateConfigs) {
                SurgeBidRequest.Imp imp = new SurgeBidRequest.Imp();
                // 生成唯一id: rdpImpId_templateId_timestamp
                String impId = String.format("%s_%s_%d", rdpImp.getId(), rdpAsset.getTemplateId(), System.currentTimeMillis());
                imp.setId(impId);
                
                // 将impId和templateId的映射添加到impToTemplateMap中
                impToTemplateMap.put(impId, rdpAsset.getTemplateId());
                
                imp.setBid_type(rdpImp.getBidType());
                imp.setBid_floor(rdpImp.getBidFloor());
                if (!AD_TYPE.containsKey(rdpImp.getAdType())) {
                    log.warn("adType not support: {}", rdpImp.getAdType());
                    continue;
                }
                imp.setAd_type(AD_TYPE.get(rdpImp.getAdType()));
                // 模版映射
                imp.setTag_id(templateConfig.getTagId());
                List<SurgeBidRequest.Imp.Asset> assets = buildAssets(templateConfig.getAssets());
                if (CollectionUtils.isNotEmpty(assets)) {
                    log.warn("assets is empty, template_id{}", rdpAsset.getTemplateId());
                    continue;
                }
                imp.setAsset(assets);
                impList.add(imp);
            }
        }
        return Pair.of(impList, impToTemplateMap);
    }

    private List<SurgeBidRequest.Imp> deduplicateImps(List<SurgeBidRequest.Imp> imps) {
        Set<SurgeBidRequest.Imp> uniqueImps = new TreeSet<>((a, b) -> {
            // 比较 tag_id
            int tagCompare = Objects.compare(a.getTag_id(), b.getTag_id(), Comparator.nullsFirst(String::compareTo));
            if (tagCompare != 0) return tagCompare;
            
            // 比较 ad_type
            int adTypeCompare = Integer.compare(a.getAd_type(), b.getAd_type());
            if (adTypeCompare != 0) return adTypeCompare;
            
            // 比较 asset 集合（顺序无关）
            if (a.getAsset() == null && b.getAsset() == null) return 0;
            if (a.getAsset() == null) return -1;
            if (b.getAsset() == null) return 1;
            
            // 比较 asset 集合大小
            int sizeCompare = Integer.compare(a.getAsset().size(), b.getAsset().size());
            if (sizeCompare != 0) return sizeCompare;
            
            // 比较 asset 集合内容（顺序无关）
            Set<SurgeBidRequest.Imp.Asset> aSet = new HashSet<>(a.getAsset());
            Set<SurgeBidRequest.Imp.Asset> bSet = new HashSet<>(b.getAsset());
            return aSet.equals(bSet) ? 0 : 1;
        });
        
        // 添加所有元素，自动去重
        uniqueImps.addAll(imps);
        return new ArrayList<>(uniqueImps);
    }

    private List<SurgeBidRequest.Imp.Asset> buildAssets(List<SurgeRtbConfig.SurgeTemplateConfig.Asset> assets) {
        return assets.stream().map(asset -> {
            SurgeBidRequest.Imp.Asset target_asset = new SurgeBidRequest.Imp.Asset();
            target_asset.setTemplate_id(asset.getTemplateId());
            target_asset.setWidth(asset.getWidth());
            target_asset.setHeight(asset.getHeight());
            return target_asset;
        }).collect(Collectors.toList());
    }

    private Optional<SurgeBidRequest.CustomizedUserTag> buildCustomizedUserTag(RdpRequest rdpRequest, SurgeRtbConfig surgeRtbConfig) {
        Map<String, Integer> allowAppList = surgeRtbConfig.getAppListMapping();
        Set<String> appList = rdpRequest.getUserAppList();

        loggingService.logForDebug(getClass(), new LogMessageBuilder("surge_rtb build customizedUserTag")
                .addParameter("appList", appList)
                .addParameter("allowAppList", allowAppList)
        );
        if (CollectionUtils.isEmpty(appList)) {
            return Optional.empty();
        }
        Collection<String> retainAppList = CollectionUtils.retainAll(appList, allowAppList.keySet());
        if (CollectionUtils.isEmpty(retainAppList)) {
            return Optional.empty();
        }
        SurgeBidRequest.CustomizedUserTag customizedUserTag = new SurgeBidRequest.CustomizedUserTag();

        List<SurgeBidRequest.CustomizedUserTag.InstalledApp> installedApps = new ArrayList<>();
        retainAppList.forEach(app -> {
            int appId = allowAppList.get(app);
            SurgeBidRequest.CustomizedUserTag.InstalledApp install = new SurgeBidRequest.CustomizedUserTag.InstalledApp();
            install.setId(appId);
            installedApps.add(install);
        });
        customizedUserTag.setInstalled_app_list(installedApps);
        return Optional.of(customizedUserTag);
    }

    private SurgeBidRequest.App buildApp(RdpProto.BidRequest.App rdpApp) {
        SurgeBidRequest.App app = new SurgeBidRequest.App();
        app.setName(rdpApp.getName());
        app.setBundle(rdpApp.getBundle());
        app.setVersion(rdpApp.getVersion());
        return app;
    }

    private SurgeBidRequest.Device buildDevice(RdpProto.BidRequest.Device rdpDevice) {
        SurgeBidRequest.Device device = new SurgeBidRequest.Device();

        device.setUa(rdpDevice.getUa());
        device.setIp(rdpDevice.getIpv4().isEmpty() ? rdpDevice.getIpv6() : rdpDevice.getIpv4());    // TODO
        device.setDevice_type(DEVICE_TYPE.getOrDefault(rdpDevice.getDeviceType(), 0));
        device.setMake(rdpDevice.getMake());
        device.setOs(rdpDevice.getOs());
        device.setCarrier(CARRIER_MAP.getOrDefault(rdpDevice.getCarrier(), 0));
        device.setConnection_type(CONNECTION_TYPE.getOrDefault(rdpDevice.getNetworkType(), 0));

        String bootMark = rdpDevice.getBootTs();
        if (StringUtils.isNotBlank(bootMark)) {
            device.setBoot_mark(rdpDevice.getBootTs());
        }
        String updateMark = rdpDevice.getUpdateTs();
        if (StringUtils.isNotBlank(updateMark)) {
            device.setUpdate_mark(rdpDevice.getUpdateTs());
        }

        device.setOsv(rdpDevice.getOsv());
        device.setModel(rdpDevice.getModel());
        // TODO: IpadOs?
        if (rdpDevice.getOs().equals(DeviceOs.IOS.getOsName())) {
            device.setIdfa(rdpDevice.getIdfa());
            device.setCaid_infos(buildCaids(rdpDevice.getCaidsList()));
        } else {
            device.setImei(rdpDevice.getImei());
            device.setOaid(rdpDevice.getOaid());
        }
        return device;
    }

    private List<SurgeBidRequest.Device.CaidInfo> buildCaids(List<RdpProto.BidRequest.Caid> caidList) {
        if (CollectionUtils.isNotEmpty(caidList)) {
            return caidList.stream().map(caid -> {
                SurgeBidRequest.Device.CaidInfo caidInfo = new SurgeBidRequest.Device.CaidInfo();
                caidInfo.setCaid(caid.getId());
                caidInfo.setVersion(caid.getVersion());
                return caidInfo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 同步更新impId到templateId的映射，确保与去重后的imp列表保持一致
     * 
     * @param impToTemplateMap impId到templateId的映射
     * @param uniqueImps 去重后的imp列表
     */
    private void syncTemplateToImpMapping(Map<String, String> impToTemplateMap, List<SurgeBidRequest.Imp> uniqueImps) {
        // 获取去重后的imp ID集合
        Set<String> uniqueImpIds = uniqueImps.stream()
                .map(SurgeBidRequest.Imp::getId)
                .collect(Collectors.toSet());
                
        // 移除不在去重后imp列表中的imp ID
        impToTemplateMap.entrySet().removeIf(entry -> !uniqueImpIds.contains(entry.getKey()));
    }
}
