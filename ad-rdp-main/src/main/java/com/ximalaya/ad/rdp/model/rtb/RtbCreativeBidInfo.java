package com.ximalaya.ad.rdp.model.rtb;

import com.ximalaya.ad.rdp.model.BidStat;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class RtbCreativeBidInfo extends CreativeBidInfo<RtbAdCreative> {

    private String templateId;

    @Override
    public BigDecimal getBidPrice() {
        return getAdCreative().getPrice();
    }
} 