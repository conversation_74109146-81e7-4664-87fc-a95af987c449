package com.ximalaya.ad.rdp.vo.request;

import com.ximalaya.ad.rdp.model.BidStat;
import com.ximalaya.ad.rdp.model.Index;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.retrieval.local.IndexServer;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class Context {

    private ThreadLocal<ContextInfo> contextInfoThreadLocal = ThreadLocal.withInitial(ContextInfo::new);
    @Autowired
    private IndexServer indexServer;

    public Index getIndex() {
        return contextInfoThreadLocal.get().index;
    }

    public RequestBidStat getOrCreateRequestBidStat(Integer dspId) {
        return contextInfoThreadLocal.get().getOrCreateRequestBidStat(dspId);
    }

    public List<RequestBidStat> getAllRequestBidStats() {
        return contextInfoThreadLocal.get().getAllRequestBidStats();
    }

    public void setRequestId(long requestId) {
        contextInfoThreadLocal.get().setRequestId(requestId);
    }

    public long getRequestId() {
        return contextInfoThreadLocal.get().requestId;
    }

    public void clear() {
        contextInfoThreadLocal.remove();
    }

    @Data
    private class ContextInfo {
        private Index index;
        private long requestId;
        private final Map<Integer, RequestBidStat> dspIdToRequestBidStat = new ConcurrentHashMap<>();
        
        public ContextInfo() {
            this.index = indexServer.getIndex();
        }
        
        public RequestBidStat getOrCreateRequestBidStat(Integer dspId) {
            return dspIdToRequestBidStat.computeIfAbsent(dspId, k -> RequestBidStat.builder().dspId(dspId).build());
        }
        
        public List<RequestBidStat> getAllRequestBidStats() {
            return new ArrayList<>(dspIdToRequestBidStat.values());
        }
    }
}
