package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import io.netty.handler.codec.http.cookie.Cookie;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.cookie.CookieStore;
import org.asynchttpclient.uri.Uri;

import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

/**
 * 禁用 Cookie 的存储实现，所有方法都是空实现或返回空值/默认值
 * 用于在需要禁用 Cookie 的 HTTP 请求中
 */
@Slf4j
public class DisabledCookieStore implements CookieStore {

    @Override
    public void add(Uri uri, Cookie cookie) {
        // 空实现，不存储任何 Cookie
    }

    @Override
    public List<Cookie> get(Uri uri) {
        return Collections.emptyList(); // 始终返回空列表
    }

    @Override
    public List<Cookie> getAll() {
        return Collections.emptyList(); // 始终返回空列表
    }

    @Override
    public boolean remove(Predicate<Cookie> predicate) {
        return true; // 始终返回成功
    }

    @Override
    public boolean clear() {
        return true; // 始终返回成功
    }

    @Override
    public void evictExpired() {
        // 空实现，不执行任何操作
    }

    @Override
    public int incrementAndGet() {
        return 0; // 始终返回 0
    }

    @Override
    public int decrementAndGet() {
        return 0; // 始终返回 0
    }

    @Override
    public int count() {
        return 0; // 始终返回 0
    }
} 