package com.ximalaya.ad.rdp.rti.asynchttp;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.netty.channel.EventLoop;
import io.netty.channel.EventLoopGroup;
import io.netty.resolver.NameResolver;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;
import io.netty.util.internal.SocketUtils;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * RDP 域名解析器实现
 * 提供 DNS 解析缓存和异步解析功能
 * 
 * 优化说明：
 * 1. 使用 JDK 21 虚拟线程替代传统线程池
 * 2. 使用文本块优化日志输出
 * 3. 使用 Optional 优化空值处理
 * 4. 改进错误处理和资源管理
 */
@Slf4j
public class DspNameResolver implements NameResolver<InetAddress> {

    private LoadingCache<String, List<InetAddress>> nameCache;
    private EventLoopGroup eventExecutors;
    private ExecutorService virtualThreadExecutor;

    private static final int CONCURRENT = 4;
    private static final int CACHE_REFRESH_HOURS = 1;

    /**
     * 构造函数
     *
     * @param eventLoopGroup 事件循环组
     */
    public DspNameResolver(EventLoopGroup eventLoopGroup) {
        this.eventExecutors = eventLoopGroup;
        this.virtualThreadExecutor = createVirtualThreadExecutor();
        init();
    }

    /**
     * 创建虚拟线程执行器
     */
    private ExecutorService createVirtualThreadExecutor() {
        ThreadFactory virtualThreadFactory = Thread.ofVirtual()
                .name("dsp-resolver-", 0)
                .factory();
        return Executors.newThreadPerTaskExecutor(virtualThreadFactory);
    }

    /**
     * 初始化缓存
     */
    private void init() {
        nameCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(CACHE_REFRESH_HOURS, TimeUnit.HOURS)
                .concurrencyLevel(CONCURRENT)
                .recordStats()
                .build(new CacheLoader<String, List<InetAddress>>() {
                    @Override
                    public List<InetAddress> load(String key) throws Exception {
                        return Lists.newArrayList(SocketUtils.allAddressesByName(key));
                    }
                });
    }

    @Override
    public Future<InetAddress> resolve(String s) {
        EventLoop eventLoop = eventExecutors.next();
        Promise<InetAddress> promise = eventLoop.newPromise();
        
        virtualThreadExecutor.execute(() -> {
            try {
                List<InetAddress> addresses = nameCache.get(s);
                if (addresses.isEmpty()) {
                    promise.setFailure(new RuntimeException("No addresses found for domain: " + s));
                } else {
                    promise.setSuccess(addresses.get(0));
                }
            } catch (Throwable e) {
                log.error("""
                    Failed to resolve domain: {}
                    Error: {}
                    """, s, e.getMessage(), e);
                promise.setFailure(e);
            }
        });
        
        return promise;
    }

    @Override
    public Future<InetAddress> resolve(String s, Promise<InetAddress> promise) {
        virtualThreadExecutor.execute(() -> {
            try {
                List<InetAddress> addresses = nameCache.get(s);
                if (addresses.isEmpty()) {
                    promise.setFailure(new RuntimeException("No addresses found for domain: " + s));
                } else {
                    promise.setSuccess(addresses.get(0));
                }
            } catch (Throwable e) {
                log.error("""
                    Failed to resolve domain: {}
                    Error: {}
                    """, s, e.getMessage(), e);
                promise.setFailure(e);
            }
        });
        
        return promise;
    }

    @Override
    public Future<List<InetAddress>> resolveAll(String s) {
        EventLoop eventLoop = eventExecutors.next();
        Promise<List<InetAddress>> promise = eventLoop.newPromise();
        
        virtualThreadExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s));
            } catch (Throwable e) {
                log.error("""
                    Failed to resolve all addresses for domain: {}
                    Error: {}
                    """, s, e.getMessage(), e);
                promise.setFailure(e);
            }
        });
        
        return promise;
    }

    @Override
    public Future<List<InetAddress>> resolveAll(String s, Promise<List<InetAddress>> promise) {
        virtualThreadExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s));
            } catch (Throwable e) {
                log.error("""
                    Failed to resolve all addresses for domain: {}
                    Error: {}
                    """, s, e.getMessage(), e);
                promise.setFailure(e);
            }
        });
        
        return promise;
    }

    /**
     * 预解析域名
     *
     * @param s 域名
     */
    public void preResolveAll(String s) {
        virtualThreadExecutor.execute(() -> {
            try {
                nameCache.get(s);
                log.debug("Pre-resolved domain: {}", s);
            } catch (Throwable e) {
                log.warn("Pre-resolve failed for domain: {}, error: {}", s, e.getMessage());
            }
        });
    }

    /**
     * 立即解析域名
     *
     * @param s 域名
     * @return IP 地址列表的 Optional
     */
    public Optional<List<InetAddress>> resolveAllImmediately(String s) {
        try {
            List<InetAddress> addresses = nameCache.get(s);
            return addresses.isEmpty() ? Optional.empty() : Optional.of(addresses);
        } catch (Throwable e) {
            log.error("""
                Failed to resolve domain immediately: {}
                Error: {}
                """, s, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return nameCache.stats().toString();
    }

    /**
     * 清理缓存
     */
    public void invalidateCache() {
        nameCache.invalidateAll();
        log.info("DNS cache invalidated");
    }

    @Override
    public void close() {
        try {
            if (virtualThreadExecutor != null && !virtualThreadExecutor.isShutdown()) {
                virtualThreadExecutor.shutdown();
                if (!virtualThreadExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    virtualThreadExecutor.shutdownNow();
                }
            }
            
            if (eventExecutors != null && !eventExecutors.isShutdown()) {
                eventExecutors.shutdownGracefully();
            }
            
            log.info("DspNameResolver closed successfully");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while closing DspNameResolver", e);
        }
    }
}
