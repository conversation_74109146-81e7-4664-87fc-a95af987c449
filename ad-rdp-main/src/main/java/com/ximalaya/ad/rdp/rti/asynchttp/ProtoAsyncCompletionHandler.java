package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import com.github.luben.zstd.Zstd;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.service.impl.LoggingServiceImpl;
import com.ximalaya.ad.rdp.stat.ResultCode;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.Response;

import java.util.List;

/**
 * 异步响应处理器-protobuf
 * @param <T> 响应解析后的类型
 */
@Slf4j
public class ProtoAsyncCompletionHandler<T> extends AbstractAsyncCompletionHandler<T> {

    private static final String DSPID = "dspid";
    private static final String MESSAGE = "message";
    private static final String RESPONSE = "response";

    private ThrowingFunction<byte[], T> responseParser;
    private boolean preRequestResult;
    private boolean isCompress;                             // 处理响应数据前，需要先解压缩

    private ProtoAsyncCompletionHandler(long requestId, boolean canLog, List<RequestBidStat> requestBidStats) {
        super(requestId, canLog, requestBidStats);
    }

    public static <T> ProtoAsyncCompletionHandler<T> getInstance(ThrowingFunction<byte[], T> responseParser, List<RequestBidStat> requestBidStats,
                                                                 long requestId, boolean canLog, boolean preRequestResult,
                                                                 boolean isCompress) {
        ProtoAsyncCompletionHandler<T> handler = new ProtoAsyncCompletionHandler<>(requestId, canLog, requestBidStats);
        handler.responseParser = responseParser;
        handler.preRequestResult = preRequestResult;
        handler.isCompress = isCompress;
        return handler;
    }

    @Override
    public T parseResponse(Response response) {
        if (DspAsyncHttpClientImpl.noNeedParseBody(response, requestBidStats, requestIdNumber, canLog, preRequestResult)) {
            return null;
        }
        T t = null;
        byte[] body;
        try {
            body = response.getResponseBodyAsBytes();
            if (isCompress) {
                body = Zstd.decompress(body, (int)Zstd.decompressedSize(body));
            }
        } catch (Exception e) {
            log.warn(LogMessageBuilder.MESSAGE_HOLDER,
                    new LogMessageBuilder("response read to bytes error!")
                            .addParameter(DSPID, requestBidStats.getFirst().getDspId())
                            .addParameter("responseId",  requestBidStats.getFirst().getRequestId())
                            .addParameter(MESSAGE, e.getMessage()));
            requestBidStats.forEach(bidStat -> bidStat.setResult(ResultCode.BODY_READ_ERR, preRequestResult));
            return null;
        }
        if (0 == body.length) {
            requestBidStats.forEach(bidStat -> bidStat.setResult(ResultCode.BODY_EMPTY, preRequestResult));
        } else {
            try {
                t = responseParser.apply(body);
                if(t instanceof MessageOrBuilder) {
                    final T finalT = t;
                    LoggingServiceImpl.logCanForDebug(getClass(), new LogMessageBuilder("get response")
                                    .addParameter(DSPID, requestBidStats.getFirst().getDspId())
                                    .addParameter("responseId", requestBidStats.getFirst().getRequestId())
                                    .addLazyParam(RESPONSE, () -> TextFormat.shortDebugString((MessageOrBuilder) finalT)),
                            canLog, requestIdNumber);
                }
            } catch (Exception e) {
                log.warn(LogMessageBuilder.MESSAGE_HOLDER,
                        new LogMessageBuilder("response protobuf parse error!")
                                .addParameter(DSPID, requestBidStats.getFirst().getDspId())
                                .addParameter("body", body)
                                .addParameter("responseId",  requestBidStats.getFirst().getRequestId())
                                .addParameter(MESSAGE, e.getMessage()));
                requestBidStats.forEach(bidStat -> bidStat.setResult(ResultCode.BODY_PARSE_ERR, preRequestResult));
            }
        }
        return t;
    }
}
