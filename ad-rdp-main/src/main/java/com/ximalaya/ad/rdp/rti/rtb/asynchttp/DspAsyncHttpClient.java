package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.model.BidStat;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.Data;
import org.asynchttpclient.RequestBuilder;
import org.asynchttpclient.netty.channel.DefaultChannelPool;

import java.util.List;
import java.util.function.Consumer;

/**
 * DspAsyncHttpClient
 *
 * @version 2025/04/07 14:24
 * @param <T> 请求DSP内容
 * @param <R> DSP响应内容
 * @param <U> 转换后的响应内容
 **/
public interface DspAsyncHttpClient<T, R> {
    @Data
    class DspAsyncHttpClientConfig {
        private int ioThreads;
        private int maxConnectionPerDsp;
        private int initConnectionPerDsp;
        private String userAgent;
        private DefaultChannelPool.PoolLeaseStrategy poolLeaseStrategy;
    }

    RequestBuilder post(String url, DspCommonConfig config);

    /**
     * 处理JSON请求并返回Proto格式响应的方法
     * @param rdpRequest SSP请求对象
     * @param requestBuilder 请求构建器
     * @param requestBody JSON请求体
     * @param responseParser Proto响应解析器
     * @param requestBidStats 请求出价统计列表
     * @return ThirdDspFuture 异步响应future
     */
    ThirdDspFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                             RequestBuilder requestBuilder,
                                             T requestBody,
                                             ThrowingFunction<byte[], R> responseParser,
                                             List<RequestBidStat> requestBidStats);
}
