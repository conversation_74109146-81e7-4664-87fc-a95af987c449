package com.ximalaya.ad.rdp.stat;

public enum ResultCode {
    // 出单成功(10xxx)
    OK(10001),   // 出单成功
    PRELOAD(10002), // 出单成功，但是将被用作预加载，不会展示
    CLIENT_DSP_BACKUP(10003), // 出单成功，但是作为广点通等客户端请求DSP的备选物料，可能不会展示
    EXTENDED_EXPOSE(10004), // 出单成功，但是作为顺延展示物料，可能不会被展示


    // 出单(11xxx)
    JUST_RECV(11001),       // 非程序化订单，默认初始状态，此时刚收到引擎(se/brand/mt/bidding)的响应 

    // 处理(13xxx)
    DSP_ACCEPT(13000),      // DSP接收流量并返回了物料


    // 回吐(14xxx)
    DSP_REFUSE(14001),      // DSP不要这次机会，回吐物料


    // 推送(15xxx)
    READY_REQ(15000),       // 准备请求DSP
    NOT200OR204(15002),     // DSP响应状态码不是200或204
    BODY_EMPTY(15003),      // 返回的消息内容为空
    BODY_READ_ERR(15004),   // 读取消息内容失败
    BODY_PARSE_ERR(15005),  // 解析消息内容失败

    DSP_UNKNOWN_ERR(15008), // 请求DSP出现未知异常
    HTTP_READ_TIMEOUT(15009), // HTTP读取超时
    HTTP_REQUEST_TIMEOUT(15010), // HTTP请求超时
    ASYNC_FUTURE_TIMEOUT(15012), // AsyncHttpClient getFuture超时
    ASYNC_FUTURE_CANCEL(15013),  // AsyncHttpClient future cancel
    ASYNC_FUTURE_INTERRUPTED(15014),  // AsyncHttpClient future interrupted
    ;

    private final int code;
    ResultCode(int code) {
        this.code = code;
    }
    public int getCode() { return code; }

} 