package com.ximalaya.ad.rdp.rti.rtb;

import com.ximalaya.ad.rdp.model.AdCreative;
import com.ximalaya.ad.rdp.model.AdGroup;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import com.ximalaya.ad.rdp.vo.request.Context;

import java.util.List;

public interface RtbApi {
    List<CreativeBidInfo<? extends AdCreative>> hit(RdpRequest request, List<AdGroup> adGroup, Context context);
    int accountId();

}
