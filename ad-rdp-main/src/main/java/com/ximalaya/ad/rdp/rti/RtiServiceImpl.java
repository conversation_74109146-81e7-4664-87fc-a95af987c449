package com.ximalaya.ad.rdp.rti;

import com.google.common.collect.Lists;
import com.ximalaya.ad.rdp.model.Account;
import com.ximalaya.ad.rdp.model.AdGroup;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.model.Index;
import com.ximalaya.ad.rdp.model.enm.RtaType;
import com.ximalaya.ad.rdp.rti.rta.RtaApi;
import com.ximalaya.ad.rdp.rti.rtb.RtbApi;
import com.ximalaya.ad.rdp.vo.request.Context;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("rtiServiceImpl")
public class RtiServiceImpl implements RtiService {

    @Resource(name = "surgeRtbApi")
    private RtbApi surgeRtbApi;

    @Autowired
    private Context context;

    @Value("${timeout.rti}")
    private long timeoutMs;

    private ExecutorService virtualThreadExecutor;
    private Map<Integer, RtaApi> rtaApiMap;
    private Map<Integer, RtbApi> rtbApiMap;

    @PostConstruct
    public void init() {
        virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
        rtaApiMap = Arrays.stream(getClass().getDeclaredFields())
                .filter(field -> RtaApi.class.isAssignableFrom(field.getType()))
                .map(field -> {
                    try {
                        return (RtaApi) field.get(this);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toMap(RtaApi::accountId, api -> api));
        rtbApiMap = Arrays.stream(getClass().getDeclaredFields())
                .filter(field -> RtbApi.class.isAssignableFrom(field.getType()))
                .map(field -> {
                    try {
                        return (RtbApi) field.get(this);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toMap(RtbApi::accountId, api -> api));
    }

    public List<CreativeBidInfo> pick(RdpRequest request, List<AdGroup> adGroupList) {
        Index index = context.getIndex();
        Map<Integer, Account> accountMap = index.getAccountList().stream().collect(Collectors.toMap(Account::getId, a -> a));
        Map<Integer, List<AdGroup>> adGroupByAccountId = adGroupList.stream().collect(Collectors.groupingBy(AdGroup::getAccount));
        List<CompletableFuture<List<AdGroup>>> rtiFutures = Lists.newArrayList();
        List<AdGroup> genAdGroups = Lists.newArrayList();
        /**
         * *** 此处暂时仅考虑只支持一次请求一个ad_group，后续根据需求再考虑支持多个 ***
         */
        adGroupByAccountId.forEach((accountId, adGroupOfAccount) -> {
            Account account = accountMap.get(accountId);
            switch (account.getRtaType()) {
                case RtaType.RTA:
                    Optional.ofNullable(rtaApiMap.get(accountId))
                            .ifPresent(rtaApi ->
                                    adGroupOfAccount.forEach(adGroup -> rtiFutures.add(CompletableFuture.supplyAsync(
                                            () -> rtaApi.hit(request, Collections.singletonList(adGroup)), virtualThreadExecutor)
                                    )));
                    break;
                // Note: ad_creative替换物料
                case RtaType.RTB:
                    Optional.ofNullable(rtbApiMap.get(accountId))
                            .ifPresent(rtbApi ->
                                    adGroupOfAccount.forEach(adGroup -> rtiFutures.add(CompletableFuture.supplyAsync(
                                            () -> rtbApi.hit(request, Collections.singletonList(adGroup), context), virtualThreadExecutor)
                                    )));
                    break;
                default:
                    genAdGroups.addAll(adGroupOfAccount);
            }
        });
        try {
            CompletableFuture.allOf(rtiFutures.toArray(new CompletableFuture[0])).get(timeoutMs, TimeUnit.MILLISECONDS);
            return Stream.concat(genAdGroups.stream(),
                    rtiFutures.stream().filter(CompletableFuture::isDone).map(CompletableFuture::resultNow)
                            .flatMap(List::stream)).toList();
        } catch (Exception e) {
            log.error("RtiService pick future get error.", e);
        }
        return genAdGroups;
    }
}
