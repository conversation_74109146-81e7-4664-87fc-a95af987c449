package com.ximalaya.ad.rdp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ximalaya.ad.rdp.service.SchedulerService;
import com.ximalaya.football.client.model.ConfigChangeEvent;
import com.ximalaya.football.client.spring.annotation.FootballConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class SchedulerServiceImpl implements SchedulerService {

    private static final String COMMON_POOL_EXECUTOR = "common_pool_executor";

    private final Map<String, ThreadPoolExecutor> poolExecutorMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {
        // Initialize common executor
        poolExecutorMap.put(COMMON_POOL_EXECUTOR, createExecutor(
            COMMON_POOL_EXECUTOR,
            4,  // coreSize
            8,  // maxSize
            5,  // keepAliveTime (minutes)
            2000 // queueSize
        ));
    }

    @PreDestroy
    public void destroy() {
        poolExecutorMap.forEach((name, executor) -> {
            try {
                executor.shutdown();
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("ThreadPool {} has been shutdown", name);
        });
    }

    @Override
    public ThreadPoolExecutor getCommonPoolExecutor() {
        return poolExecutorMap.get(COMMON_POOL_EXECUTOR);
    }

    @FootballConfigChangeListener("rdp_executor_config")
    public void change(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(COMMON_POOL_EXECUTOR)) {
            updateExecutor(COMMON_POOL_EXECUTOR, changeEvent.getChange(COMMON_POOL_EXECUTOR).getNewValue());
        }
    }

    private void updateExecutor(String executorName, String newConfig) {
        try {
            PoolExecutorConfig config = JSONObject.parseObject(newConfig, PoolExecutorConfig.class);
            ThreadPoolExecutor newExecutor = createExecutor(
                executorName,
                config.getCoreSize(),
                config.getMaxSize(),
                config.getKeepAliveTime(),
                config.getQueueSize()
            );

            ThreadPoolExecutor oldExecutor = poolExecutorMap.put(executorName, newExecutor);
            if (oldExecutor != null) {
                shutdownExecutor(oldExecutor, executorName);
            }
        } catch (Exception e) {
            log.error("Failed to update executor: {}", executorName, e);
        }
    }

    private ThreadPoolExecutor createExecutor(String poolName, int coreSize, int maxSize,
                                            long keepAliveTime, int queueSize) {
        return new ThreadPoolExecutor(
            coreSize,
            maxSize,
            keepAliveTime,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(queueSize),
            new NamedThreadFactory(poolName),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    private void shutdownExecutor(ThreadPoolExecutor executor, String name) {
        try {
            executor.shutdown();
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
            log.info("Old ThreadPool {} has been shutdown", name);
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
            log.warn("Shutdown of {} was interrupted", name);
        }
    }

    private static class NamedThreadFactory implements ThreadFactory {
        private final String prefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        NamedThreadFactory(String prefix) {
            this.prefix = prefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, prefix + "-" + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    private static class PoolExecutorConfig {
        private int coreSize;
        private int maxSize;
        private long keepAliveTime;
        private int queueSize;

        // Getters and setters
        public int getCoreSize() { return coreSize; }
        public void setCoreSize(int coreSize) { this.coreSize = coreSize; }
        public int getMaxSize() { return maxSize; }
        public void setMaxSize(int maxSize) { this.maxSize = maxSize; }
        public long getKeepAliveTime() { return keepAliveTime; }
        public void setKeepAliveTime(long keepAliveTime) { this.keepAliveTime = keepAliveTime; }
        public int getQueueSize() { return queueSize; }
        public void setQueueSize(int queueSize) { this.queueSize = queueSize; }
    }
} 