package com.ximalaya.ad.rdp.rti.rtb.surge;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * SurgeBidRequest 巨浪请求json
 *
 * @version 2025/04/07 11:15
 **/
@Data
public class SurgeBidRequest {
    private String id;                     // 请求ID
    private String api_version;            // API版本
    private int test;                      // 测试标识 0:正式流量 1:测试流量
    private List<Imp> imp;                 // 广告位信息
    private App app;                       // 应用信息
    private Device device;                 // 设备信息
    private Pmp pmp;                      // PMP交易信息
    private CustomizedUserTag customized_user_tag;  // 用户标签信息

    @Data
    public static class Imp {
        private String id;                 // 广告位ID
        private String tag_id;             // 广告位标识
        private int ad_type;               // 广告类型
        private int bid_type;              // 竞价类型 0:一价 1:二价
        private long bid_floor;            // 底价 单位:分
        private List<Asset> asset;         // 素材信息

        // 自定义比较函数
        public boolean requestEquals(Imp other) {
            if (other == null) return false;
            if (!Objects.equals(this.tag_id, other.tag_id)) return false;
            if (!Objects.equals(this.ad_type, other.ad_type)) return false;
            if (this.asset == null && other.asset == null) return true;
            if (this.asset == null || other.asset == null) return false;
            return new HashSet<>(this.asset).equals(new HashSet<>(other.asset));
        }

        @Data
        public static class Asset {
            private String template_id;       // 模板ID
            private int height;            // 高度
            private int width;             // 宽度
        }
    }

    @Data
    public static class App {
        private String name;               // 应用名称
        private String bundle;             // 应用包名
        private String version;            // 应用版本
    }

    @Data
    public static class Device {
        private String ua;                 // User-Agent
        private String ip;                 // IP地址
        private int device_type;           // 设备类型
        private String make;               // 设备制造商
        private String model;              // 设备型号
        private String os;                 // 操作系统
        private String osv;                // 系统版本
        private int carrier;               // 运营商
        private int connection_type;       // 网络类型
        private String boot_mark;          // 开机标识
        private String update_mark;        // 更新标识
        private String imei;               // IMEI
        private String oaid;               // OAID
        private String idfa;               // IDFA
        private List<CaidInfo> caid_infos; // CAID信息

        @Data
        public static class CaidInfo {
            private String caid;           // CAID值
            private String version;           // CAID版本
        }
    }

    @Data
    public static class Pmp {
        private String deal_id;            // 交易ID
        private int pmp_bid_type;          // PMP竞价类型
        private int pmp_price;             // PMP出价 单位:分
    }

    @Data
    public static class CustomizedUserTag {
        private List<InstalledApp> installed_app_list;  // 已安装应用列表

        @Data
        public static class InstalledApp {
            private int id;                // 应用ID
        }
    }
}
