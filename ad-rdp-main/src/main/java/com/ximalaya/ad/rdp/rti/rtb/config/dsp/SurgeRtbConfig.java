package com.ximalaya.ad.rdp.rti.rtb.config.dsp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class SurgeRtbConfig extends DspCommonConfig<SurgeRtbConfig.SurgeTemplateConfig>{
    private String apiVersion;
    private String priceEncryptToken;

    private Map<String, Integer> appListMapping;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class SurgeTemplateConfig extends TemplateConfig {
        private String tagId;
        private List<Asset> assets;
        @Data
        public static class Asset {
            private String templateId;
            private int width;
            private int height;
        }
    }
} 