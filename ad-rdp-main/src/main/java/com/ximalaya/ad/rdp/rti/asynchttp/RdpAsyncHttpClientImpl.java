package com.ximalaya.ad.rdp.rti.asynchttp;

import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.RdpFootballCommonConfig;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.service.SchedulerService;
import com.ximalaya.ad.rdp.util.RdpCommonJsonUtils;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class RdpAsyncHttpClientImpl<T, R> implements RdpAsyncHttpClient<T, R> {

    @Autowired
    private LoggingService loggingService;
    
    @Autowired
    private SchedulerService schedulerService;
    
    @Autowired
    private RdpFootballCommonConfig footballCommonConfig;

    private AsyncHttpClient client;

    @PostConstruct
    public void init() {
        initClient(footballCommonConfig.getDspAsyncHttpClient());
    }

    private void initClient(RdpAsyncHttpClientConfig config) {
        var clientConfig = new DefaultAsyncHttpClientConfig.Builder()
                .setMaxConnectionsPerHost(config.getMaxConnectionPerDsp())
                .setUserAgent(config.getUserAgent())
                .setKeepAlive(true)
                .setFollowRedirect(false)
                .setSoKeepAlive(true)
                .setTcpNoDelay(true)
                .setPooledConnectionIdleTimeout(0)
                .setConnectionTtl(0)
                .build();
        
        var newClient = Dsl.asyncHttpClient(clientConfig);
        var oldClient = this.client;
        this.client = newClient;
        
        log.info("RdpAsyncHttpClient initialized");
        
        if (oldClient != null) {
            try {
                oldClient.close();
            } catch (IOException e) {
                log.error("Failed to close old AsyncHttpClient", e);
            }
        }
    }

    @Override
    public RequestBuilder post(String url, DspCommonConfig config) {
        int timeout = config != null ? config.getSocketTimeout() : 5000;
        return Dsl.post(url)
                .setRequestTimeout(timeout)
                .setReadTimeout(timeout);
    }

    @Override
    public RdpAsyncFuture<R> getResponse(RdpRequest rdpRequest,
                                        RequestBuilder requestBuilder,
                                        T requestBody,
                                        ThrowingFunction<byte[], R> responseParser) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }
        
        var request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addParameter("url", request.getUrl())
                        .addParameter("request", body), rdpRequest);

        var handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, List.of(), rdpRequest.getRequestIdNumber(), true, false, false);
        
        var future = client.executeRequest(request, handler);
        var rdpAsyncFuture = new RdpAsyncFuture<R>();
        rdpAsyncFuture.init(future, List.of());
        return rdpAsyncFuture;
    }

    @Override
    public RdpAsyncFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                                   RequestBuilder requestBuilder,
                                                   T requestBody,
                                                   ThrowingFunction<byte[], R> responseParser,
                                                   List<RequestBidStat> requestBidStats) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }
        
        var request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addLazyParam("dspId", () -> requestBidStats.getFirst().getDspId())
                        .addLazyParam("url", request::getUrl)
                        .addParameter("request", body), rdpRequest);

        var handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, requestBidStats, rdpRequest.getRequestIdNumber(), true, false, false);
        
        setSubmitRequestTime(requestBidStats);
        var future = client.executeRequest(request, handler);
        var rdpAsyncFuture = new RdpAsyncFuture<R>();
        rdpAsyncFuture.init(future, requestBidStats);
        return rdpAsyncFuture;
    }

    private void setSubmitRequestTime(List<RequestBidStat> requestBidStats) {
        var submitRequestTime = System.currentTimeMillis();
        requestBidStats.forEach(stat -> stat.setSubmitDspRequestTime(submitRequestTime));
    }
}