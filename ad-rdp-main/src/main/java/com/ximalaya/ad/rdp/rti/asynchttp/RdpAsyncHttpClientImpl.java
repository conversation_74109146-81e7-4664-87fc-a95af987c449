package com.ximalaya.ad.rdp.rti.asynchttp;

import com.ximalaya.ad.common.monitor.AdPrometheusService;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.RdpFootballCommonConfig;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.service.SchedulerService;
import com.ximalaya.ad.rdp.service.impl.LoggingServiceImpl;
import com.ximalaya.ad.rdp.stat.ResultCode;
import com.ximalaya.ad.rdp.util.RdpCommonJsonUtils;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import com.ximalaya.football.client.model.ConfigChangeEvent;
import com.ximalaya.football.client.spring.annotation.FootballConfigChangeListener;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.prometheus.client.Gauge;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * RdpAsyncHttpClientImpl - RDP系统异步HTTP客户端实现
 * 支持RTB、RTA等多种场景的HTTP通信，提供连接监控、配置热更新等功能
 *
 * @version 2025/07/17 优化版本
 * @param <T> 请求内容类型
 * @param <R> 响应内容类型
 */
@Slf4j
@Component
public class RdpAsyncHttpClientImpl<T, R> implements RdpAsyncHttpClient<T, R> {

    // 监控指标
    private static final Gauge RDP_CLIENT_CHANNEL_MONITOR = Gauge.build()
            .name("rdp_http_client_channel_monitor")
            .labelNames("host")
            .help("RDP HTTP client connection monitor")
            .create();

    private static final long MONITOR_VALUE_OFFSET = 100000000L;
    private static final String DSPID = "dspid";
    private static final String REQUEST = "request";
    private static final String RESPONSE = "response";

    @Autowired
    private LoggingService loggingService;

    @Autowired
    private SchedulerService schedulerService;

    @Autowired
    private RdpFootballCommonConfig footballCommonConfig;

    private AsyncHttpClient client;
    private DspNameResolver dspNameResolver;
    private EventLoopGroup eventLoopGroup;

    static {
        AdPrometheusService.getInstance().registerCollector(RDP_CLIENT_CHANNEL_MONITOR);
    }

    @PostConstruct
    public void init() {
        RdpAsyncHttpClient.RdpAsyncHttpClientConfig config = footballCommonConfig.getDspAsyncHttpClient();
        initClient(config);
        initMonitoring();
        log.info("RdpAsyncHttpClient initialized successfully");
    }

    /**
     * 初始化监控
     */
    private void initMonitoring() {
        RDP_CLIENT_CHANNEL_MONITOR.setChild(new Gauge.Child() {
            @Override
            public double get() {
                if (client == null) {
                    return 0.0;
                }
                ClientStats clientStats = client.getClientStats();
                long value = clientStats.getTotalActiveConnectionCount() * MONITOR_VALUE_OFFSET
                           + clientStats.getTotalIdleConnectionCount();
                clientStats.getStatsPerHost().forEach((host, hostStats) ->
                        RDP_CLIENT_CHANNEL_MONITOR.labels(host).set((double) (
                                hostStats.getHostActiveConnectionCount() * MONITOR_VALUE_OFFSET
                                + hostStats.getHostIdleConnectionCount())));
                return value;
            }
        }, "all");
    }

    @PreDestroy
    public void destroy() {
        if (client != null) {
            try {
                client.close();
                log.info("RdpAsyncHttpClient closed successfully");
            } catch (IOException e) {
                log.error("Failed to close RdpAsyncHttpClient", e);
            }
        }
        if (eventLoopGroup != null) {
            eventLoopGroup.shutdownGracefully();
        }
    }

    /**
     * 配置热更新监听器
     */
    @FootballConfigChangeListener(value = "rdpCommonConfig")
    public void onConfigChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("dspAsyncHttpClient")) {
            String newValue = changeEvent.getChange("dspAsyncHttpClient").getNewValue();
            RdpAsyncHttpClient.RdpAsyncHttpClientConfig newConfig =
                    RdpCommonJsonUtils.fromJsonString(RdpAsyncHttpClient.RdpAsyncHttpClientConfig.class, newValue);
            if (newConfig != null) {
                log.info("RdpAsyncHttpClient config changed, reinitializing...");
                initClient(newConfig);
            }
        }
    }

    private void initClient(RdpAsyncHttpClient.RdpAsyncHttpClientConfig config) {
        // 创建EventLoopGroup
        EventLoopGroup newEventLoopGroup = Epoll.isAvailable()
                ? new EpollEventLoopGroup(config.getIoThreads())
                : new NioEventLoopGroup(config.getIoThreads());

        // 创建DNS解析器
        DspNameResolver newDspNameResolver = new DspNameResolver(
                newEventLoopGroup,
                schedulerService.getDspNameResolverExecutor()
        );

        // 构建客户端配置
        DefaultAsyncHttpClientConfig clientConfig = new DefaultAsyncHttpClientConfig.Builder()
                .setEventLoopGroup(newEventLoopGroup)
                .setMaxConnectionsPerHost(config.getMaxConnectionPerDsp())
                .setUserAgent(config.getUserAgent())
                .setKeepAlive(true)
                .setFollowRedirect(false)
                .setSoKeepAlive(true)
                .setTcpNoDelay(true)
                .setPooledConnectionIdleTimeout(0)  // 连接永远不断开
                .setConnectionTtl(0)
                .build();

        AsyncHttpClient newClient = Dsl.asyncHttpClient(clientConfig);

        // 原子性替换
        AsyncHttpClient oldClient = this.client;
        EventLoopGroup oldEventLoopGroup = this.eventLoopGroup;
        DspNameResolver oldDspNameResolver = this.dspNameResolver;

        this.client = newClient;
        this.eventLoopGroup = newEventLoopGroup;
        this.dspNameResolver = newDspNameResolver;

        log.info("RdpAsyncHttpClient reinitialized. EventLoopGroup type: {}",
                clientConfig.getEventLoopGroup().getClass().getSimpleName());

        // 清理旧资源
        cleanupOldResources(oldClient, oldEventLoopGroup, oldDspNameResolver);
    }

    /**
     * 清理旧资源
     */
    private void cleanupOldResources(AsyncHttpClient oldClient, EventLoopGroup oldEventLoopGroup,
                                   DspNameResolver oldDspNameResolver) {
        if (oldClient != null) {
            try {
                oldClient.close();
            } catch (IOException e) {
                log.error("Failed to close old AsyncHttpClient", e);
            }
        }

        if (oldDspNameResolver != null) {
            try {
                oldDspNameResolver.close();
            } catch (Exception e) {
                log.error("Failed to close old DspNameResolver", e);
            }
        }

        if (oldEventLoopGroup != null) {
            oldEventLoopGroup.shutdownGracefully();
        }
    }

    @Override
    public RequestBuilder post(String url, DspCommonConfig config) {
        int timeout = config != null ? config.getSocketTimeout() : 5000;
        RequestBuilder builder = Dsl.post(url)
                .setRequestTimeout(timeout)
                .setReadTimeout(timeout);

        // 如果有DNS解析器，则使用它
        if (dspNameResolver != null) {
            builder.setNameResolver(dspNameResolver);
        }

        return builder;
    }

    @Override
    public RdpAsyncFuture<R> getResponse(RdpRequest rdpRequest,
                                        RequestBuilder requestBuilder,
                                        T requestBody,
                                        ThrowingFunction<byte[], R> responseParser) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }

        Request request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addParameter("url", request.getUrl())
                        .addParameter(REQUEST, body), rdpRequest);

        ProtoAsyncCompletionHandler<R> handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, List.of(), rdpRequest.getRequestIdNumber(), true, false, false);

        ListenableFuture<R> future = client.executeRequest(request, handler);
        RdpAsyncFuture<R> rdpAsyncFuture = new RdpAsyncFuture<>();
        rdpAsyncFuture.init(future, List.of());
        return rdpAsyncFuture;
    }

    @Override
    public RdpAsyncFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                                   RequestBuilder requestBuilder,
                                                   T requestBody,
                                                   ThrowingFunction<byte[], R> responseParser,
                                                   List<RequestBidStat> requestBidStats) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }

        Request request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addLazyParam(DSPID, () -> requestBidStats.getFirst().getDspId())
                        .addLazyParam("url", request::getUrl)
                        .addParameter(REQUEST, body), rdpRequest);

        ProtoAsyncCompletionHandler<R> handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, requestBidStats, rdpRequest.getRequestIdNumber(), true, false, false);

        setSubmitRequestTime(requestBidStats);
        ListenableFuture<R> future = client.executeRequest(request, handler);
        RdpAsyncFuture<R> rdpAsyncFuture = new RdpAsyncFuture<>();
        rdpAsyncFuture.init(future, requestBidStats);
        return rdpAsyncFuture;
    }

    /**
     * 设置请求提交时间
     */
    private void setSubmitRequestTime(List<RequestBidStat> requestBidStats) {
        long submitRequestTime = System.currentTimeMillis();
        requestBidStats.forEach(stat -> stat.setSubmitDspRequestTime(submitRequestTime));
    }

    /**
     * 检查响应是否需要解析Body
     * 从DspAsyncHttpClientImpl移植的状态码处理逻辑
     */
    static boolean noNeedParseBody(Response response, List<RequestBidStat> requestBidStats,
                                  long requestIdNumber, boolean canLog, boolean preRequestResult) {
        if (Objects.isNull(response)) {
            return false;
        }

        int status = response.getStatusCode();
        switch (status) {
            case 200:
                return false;
            case 204:
                requestBidStats.forEach(stat -> stat.setResult(ResultCode.DSP_REFUSE, preRequestResult));
                return true;
            default:
                requestBidStats.forEach(stat -> stat.setResult(ResultCode.NOT200OR204));
                String body = "empty";
                try {
                    body = response.getResponseBody();
                } catch (Exception ignored) {
                    // 忽略获取响应体的异常
                }
                LoggingServiceImpl.logCanForDebug(RdpAsyncHttpClientImpl.class,
                        new LogMessageBuilder("not 200 or 204")
                                .addParameter("responseCode", status)
                                .addParameter(DSPID, requestBidStats.isEmpty() ? 0 : requestBidStats.getFirst().getDspId())
                                .addParameter("responseId", requestBidStats.isEmpty() ? "" : requestBidStats.getFirst().getRequestId())
                                .addParameter(RESPONSE, body), canLog, requestIdNumber);
                return true;
        }
    }
}