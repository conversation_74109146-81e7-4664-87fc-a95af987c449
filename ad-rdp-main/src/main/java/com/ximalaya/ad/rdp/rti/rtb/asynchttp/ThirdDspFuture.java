package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import com.ximalaya.ad.rdp.model.BidStat;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.stat.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.ListenableFuture;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

/**
 * DSP异步请求Future封装
 * 继承CompletableFuture以获得更好的异步编程支持
 */
@Slf4j
public class ThirdDspFuture<T> extends CompletableFuture<T> {

    protected ListenableFuture<T> future;
    protected List<RequestBidStat> requestBidStats;

    public void init(ListenableFuture<T> future, List<RequestBidStat> requestBidStats) {
        this.future = future;
        this.requestBidStats = requestBidStats;
    }

    /**
     * 获取响应结果
     */
    public T get() {
        try {
            T response = future.get();
            return response;
        } catch (Exception e) {
            handleException(e);
            return null;
        }
    }

    /**
     * 带超时的获取响应结果
     */
    public T get(long timeout, TimeUnit unit) {
        try {
            T response = future.get(timeout, unit);
            return response;
        } catch (TimeoutException e) {
            setResultCode(ResultCode.ASYNC_FUTURE_TIMEOUT);
            log.warn("DSP request timeout, dspId={}", getDspId());
            return null;
        } catch (Exception e) {
            handleException(e);
            return null;
        }
    }

    /**
     * 取消请求
     */
    public void cancel() {
        if (!future.isDone()) {
            future.cancel(true);
        }
        setResultCode(ResultCode.ASYNC_FUTURE_CANCEL);
    }

    /**
     * 是否已完成
     */
    public boolean isDone() {
        return future.isDone();
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return future.isCancelled();
    }

    /**
     * 设置结果状态码
     */
    protected void setResultCode(ResultCode resultCode) {
        requestBidStats.forEach(stat -> stat.setResult(resultCode));
    }

    /**
     * 处理异常
     */
    protected void handleException(Exception e) {
        if (e instanceof InterruptedException) {
            // 重新设置中断状态
            Thread.currentThread().interrupt();
            setResultCode(ResultCode.ASYNC_FUTURE_INTERRUPTED);
            log.warn("DSP request interrupted! dspId={}", getDspId());
        } else if (e.getCause() instanceof TimeoutException) {
            String message = e.getCause().getMessage();
            if (message.startsWith("Read")) {
                setResultCode(ResultCode.HTTP_READ_TIMEOUT);
            } else if (message.startsWith("Request")) {
                setResultCode(ResultCode.HTTP_REQUEST_TIMEOUT);
            } else {
                setResultCode(ResultCode.DSP_UNKNOWN_ERR);
                log.warn("DSP unknown error! dspId={}, error={}", getDspId(), e.getMessage());
            }
        } else {
            setResultCode(ResultCode.DSP_UNKNOWN_ERR);
            log.warn("DSP request failed! dspId={}, error={}", getDspId(), e.getMessage());
        }
    }

    /**
     * 设置请求统计信息（用于RTA场景）
     */
    public void setRequestBidStats(List<RequestBidStat> requestBidStats) {
        this.requestBidStats = requestBidStats != null ? requestBidStats : List.of();
    }

    /**
     * 获取DSP ID（兼容无统计场景）
     */
    private int getDspId() {
        return requestBidStats.isEmpty() ? 0 : requestBidStats.getFirst().getDspId();
    }
} 