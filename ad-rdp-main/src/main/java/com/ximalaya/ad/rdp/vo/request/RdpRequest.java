package com.ximalaya.ad.rdp.vo.request;

import com.ximalaya.ad.rdp.metrics.RetCode;
import com.ximalaya.ad.rdp.proto.RdpProto.BidRequest;
import lombok.Data;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RdpRequest {
    private BidRequest bidRequest;

    private List<BidRequest.Imp> validImps; // 有效曝光机会列表
    private Map<String, List<BidRequest.Imp.Asset>> validAssets = new HashMap<>(); // 有效曝光机会对应的模板列表
    private Map<String, String> features;

    /************结果相关信息**************/
    private RetCode retCode = RetCode.INITIAL;
    private String retMsg;

    /************辅助字段**************/
    private long requestIdNumber;

    /************快捷方法***************/
    public Optional<BidRequest.Device> getDevice() {
        return Optional.ofNullable(bidRequest).map(BidRequest::getDevice);
    }

    public Optional<BidRequest.App> getApp() {
        return Optional.ofNullable(bidRequest).map(BidRequest::getApp);
    }

    public List<BidRequest.Imp> getImpList() {
        return Optional.ofNullable(bidRequest).map(BidRequest::getImpsList).orElse(Collections.emptyList());
    }

    public void addValidAssets(String impId, List<BidRequest.Imp.Asset> assets) {
        validAssets.putIfAbsent(impId, assets);
    }

    public String getRequestId() {
        return bidRequest.getId();
    }

    /**
     * 获取用户安装的app列表
     * @return
     */
    public Set<String> getUserAppList() {
        return Optional.ofNullable(bidRequest)
                .map(BidRequest::getUser)
                .map(BidRequest.User::getAppList)
                .map(appList -> appList.split(","))
                .map(apps -> Arrays.stream(apps)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }
}
