package com.ximalaya.ad.rdp.rti.rta.ximalaya;

import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.constant.DeviceOs;
import com.ximalaya.ad.rdp.model.AdGroup;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.model.rta.RtaCreativeBidInfo;
import com.ximalaya.ad.rdp.proto.XimaRtaProto;
import com.ximalaya.ad.rdp.rti.rta.RtaApi;
import com.ximalaya.ad.rdp.rti.rtb.asynchttp.DspAsyncHttpClient;
import com.ximalaya.ad.rdp.rti.rtb.asynchttp.ThirdDspFuture;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.asynchttpclient.RequestBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class XimaRtaApi implements RtaApi {

    @Autowired
    private RdpAsyncHttpClient<XimaRtaProto.Req, XimaRtaProto.Resp> rdpAsyncHttpClient;
    
    @Autowired
    private LoggingService loggingService;

    private static final int XIMA_RTA_ACCOUNT_ID = 1234567;
    private static final long DEFAULT_TIMEOUT_MS = 100; // RTA通常超时更短

    @Override
    public int accountId() {
        return XIMA_RTA_ACCOUNT_ID;
    }

    @Override
    public List<CreativeBidInfo> hit(RdpRequest request, List<AdGroup> adGroups) {
        if (CollectionUtils.isEmpty(adGroups)) {
            return List.of();
        }

        // 暂不支持批量请求
        AdGroup adGroup = adGroups.getFirst();
        
        // 构建RTA请求
        Optional<XimaRtaProto.Req> rtaRequestOpt = buildRtaRequest(request, adGroup);
        if (rtaRequestOpt.isEmpty()) {
            log.warn("Failed to build RTA request for adGroup: {}", adGroup.getId());
            return List.of();
        }

        // 发送异步请求
        ThirdDspFuture<XimaRtaProto.Resp> future = sendRtaRequest(rtaRequestOpt.get(), request);
        if (future == null) {
            return List.of();
        }

        // 等待响应
        XimaRtaProto.Resp response = future.get(DEFAULT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        if (response == null) {
            log.warn("RTA request timeout or failed for adGroup: {}", adGroup.getId());
            return List.of();
        }

        // 处理响应
        return processRtaResponse(response, adGroup);
    }

    private Optional<XimaRtaProto.Req> buildRtaRequest(RdpRequest rdpRequest, AdGroup adGroup) {
        var device = rdpRequest.getDevice();
        if (device.isEmpty()) {
            return Optional.empty();
        }

        var deviceInfo = device.get();
        var builder = XimaRtaProto.Req.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setRequestTime(System.currentTimeMillis())
                .addRtaIds(String.valueOf(adGroup.getId())); // 使用AdGroup ID作为RTA ID

        // 设置操作系统类型
        String osName = deviceInfo.getOs();
        XimaRtaProto.OsType osType = switch (osName.toLowerCase()) {
            case "android" -> XimaRtaProto.OsType.ANDROID;
            case "ios" -> XimaRtaProto.OsType.IOS;
            default -> XimaRtaProto.OsType.UNKNOWN_OS;
        };
        builder.setOsType(osType);

        // 设置设备标识符
        if (StringUtils.isNotBlank(deviceInfo.getImei())) {
            builder.setImei(deviceInfo.getImei());
            // TODO: 添加MD5加密逻辑
            // builder.setImeiMd5(md5(deviceInfo.getImei()));
        }
        
        if (StringUtils.isNotBlank(deviceInfo.getOaid())) {
            builder.setOaid(deviceInfo.getOaid());
            // TODO: 添加MD5加密逻辑
            // builder.setOaidMd5(md5(deviceInfo.getOaid()));
        }
        
        if (StringUtils.isNotBlank(deviceInfo.getIdfa())) {
            builder.setIdfa(deviceInfo.getIdfa());
            // TODO: 添加MD5加密逻辑
            // builder.setIdfaMd5(md5(deviceInfo.getIdfa()));
        }

        // 设置设备品牌
        if (StringUtils.isNotBlank(deviceInfo.getMake())) {
            builder.setBrand(deviceInfo.getMake());
        }

        // TODO: 设置渠道和签名
        // builder.setChannel(getChannel(rdpRequest));
        // builder.setSign(generateSign(builder));

        return Optional.of(builder.build());
    }

    private ThirdDspFuture<XimaRtaProto.Resp> sendRtaRequest(XimaRtaProto.Req rtaRequest, RdpRequest rdpRequest) {
        try {
            // TODO: 从配置中获取RTA服务URL
            String rtaUrl = "http://growth4adn.ximalaya.com/growth-ad-feeds-rta-web/rta/std/xima_adn_pd"; // 占位符URL
            
            var requestBuilder = rdpAsyncHttpClient.post(rtaUrl, null)
                    .addHeader("Content-Type", "application/json");

            // RTA使用简化的getResponse方法，不需要复杂统计
            return rdpAsyncHttpClient.getResponse(
                    rdpRequest,
                    requestBuilder,
                    rtaRequest,
                    XimaRtaProto.Resp::parseFrom
            );
        } catch (Exception e) {
            log.error("Failed to send RTA request", e);
            return null;
        }
    }

    private List<CreativeBidInfo> processRtaResponse(XimaRtaProto.Resp response, AdGroup adGroup) {
        loggingService.logForDebug(getClass(), 
                new LogMessageBuilder("xima_rta response").addParameter("detail", response));

        if (response.getCode() != 0) {
            log.warn("RTA response error: code={}, message={}", response.getCode(), response.getMessage());
            return List.of();
        }

        return switch (response.getResultType()) {
            case ALL -> {
                // 全部投放，使用整体质量分
                RtaCreativeBidInfo bidInfo = createRtaCreativeBidInfo(adGroup, response.getQualityScore());
                yield List.of(bidInfo);
            }
            case NONE -> {
                // 全部不投
                yield List.of();
            }
            case PART -> {
                // 部分投放，根据result列表处理
                yield response.getResultList().stream()
                        .filter(result -> String.valueOf(adGroup.getId()).equals(result.getRtaId()))
                        .map(result -> createRtaCreativeBidInfo(adGroup, result.getQualityScore()))
                        .toList();
            }
            default -> {
                log.warn("Unknown RTA result type: {}", response.getResultType());
                yield List.of();
            }
        };
    }

    private RtaCreativeBidInfo createRtaCreativeBidInfo(AdGroup adGroup, double qualityScore) {
        RtaCreativeBidInfo bidInfo = new RtaCreativeBidInfo();
        bidInfo.setAdGroup(adGroup);
        // TODO: 根据质量分调整出价或其他逻辑
        log.debug("RTA quality score for adGroup {}: {}", adGroup.getId(), qualityScore);
        return bidInfo;
    }
}
