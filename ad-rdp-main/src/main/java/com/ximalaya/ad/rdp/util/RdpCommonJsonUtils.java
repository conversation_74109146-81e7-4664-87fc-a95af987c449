package com.ximalaya.ad.rdp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.exception.CommonException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * CommonJsonUtils
 *
 * @version 2025/04/07 16:15
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class RdpCommonJsonUtils {
    private static final int MAX_JSON_LENGTH = 4096;

    /**
     * Convert the object to json string.
     */
    public static String toJsonString(Object object) {
        try {
            return JSON.toJSONString(object);
        } catch (Exception e) {
            String message = new LogMessageBuilder("rdp convert object to json string exception").addParameter(
                    "object", object).toString();
            log.warn(message, e);
            throw new CommonException(message, e);
        }
    }

    public static String toJsonString(Object object, SerializerFeature... features) {
        try {
            return JSON.toJSONString(object, features);
        } catch (Exception e) {
            String message = new LogMessageBuilder("rdp convert object to json string exception").addParameter(
                    "object", object).toString();
            log.warn(message, e);
            throw new CommonException(message, e);
        }
    }

    /**
     * Convert json string to object.
     */
    public static <T> T fromJsonString(Class<T> clazz, String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            String message = new LogMessageBuilder("rdp convert json string to object exception").addParameter(
                    "clazz", clazz).addParameter("jsonString",
                    StringUtils.substring(jsonString, 0, MAX_JSON_LENGTH)).toString();
            log.warn(message, e);
            throw new CommonException(message, e);
        }
    }

    /**
     * Convert json string to collection.
     */
    public static <T> T fromCollectionJsonString(TypeReference<T> clazz, String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            String message = new LogMessageBuilder("rdp convert json string to collection exception").addParameter(
                    "clazz", clazz).addParameter("jsonString",
                    StringUtils.substring(jsonString, 0, MAX_JSON_LENGTH)).toString();
            log.warn(message, e);

            throw new CommonException(message, e);
        }
    }
}
