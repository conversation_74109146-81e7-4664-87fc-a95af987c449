package com.ximalaya.ad.rdp.rti.rtb;

import com.ximalaya.ad.rdp.constant.MaterialType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.HashMap;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 处理模板配置和素材填充的处理器
 */
@Slf4j
@Component
public class TemplateMaterialProcessor {

    /**
     * 获取模板配置
     * @param templateId 模板ID
     * @return 模板配置字符串
     */
    public String getTemplateConfig(String templateId) {
        // TODO: 这里需要根据实际业务逻辑实现
        // 可以从数据库、缓存或其他地方获取模板配置
        // 这里先返回一个示例配置
        switch (templateId) {
            case "HORIZONTAL_IMAGE_1_TEXT_1":
                return "hImage,title";
            case "HORIZONTAL_VIDEO_IMAGE_TEXT_1":
                return "hVideo|hImage,title";
            default:
                return null;
        }
    }

    /**
     * 通用的填充方法，admExtractor 由调用方提供
     */
    public <T> Map<String, String> fillMaterialsByTemplate(
            String templateId,
            T adm,
            BiFunction<String, T, Map<String, String>> admExtractor) {
        // 1. 获取模板配置, 格式如 "hImage,title" 或 "hVideo|hImage,title"
        String templateConfig = getTemplateConfig(templateId);
        if (StringUtils.isEmpty(templateConfig)) {
            log.warn("template config is empty for templateId: {}", templateId);
            return new HashMap<>();
        }

        // 2. admExtractor 负责根据 templateConfig 和 adm 生成 materials
        return admExtractor.apply(templateConfig, adm);
    }

    /**
     * 通用的填充方法
     * @param templateId     模板Id
     * @param adm            DSP返回的adm对象
     * @param extractorMap   materialType到提取函数的映射, 由调用方提供
     */
    public <T> Map<String, String> fillMaterialsByTemplate(
            String templateId,
            T adm,
            Map<MaterialType, Function<T, String>> extractorMap) {
        // 1. 获取模板配置
        String templateConfig = getTemplateConfig(templateId);
        if (StringUtils.isEmpty(templateConfig)) {
            log.warn("template config is empty for templateId: {}", templateId);
            return new HashMap<>();
        }

        // 2. 提取物料
        return extractMaterials(templateConfig, adm, extractorMap);
    }

    /**
     * 通用的物料提取方法
     * @param templateConfig 模板配置字符串
     * @param adm            DSP返回的adm对象
     * @param extractorMap   materialType到提取函数的映射, 由调用方提供
     */
    public static <T> Map<String, String> extractMaterials(
            String templateConfig,
            T adm,
            Map<MaterialType, Function<T, String>> extractorMap) {
        Map<String, String> result = new HashMap<>();
        if (StringUtils.isEmpty(templateConfig) || adm == null) {
            return result;
        }
        String[] materialGroups = templateConfig.split(",");
        for (String group : materialGroups) {
            String[] materialTypes = group.split("\\|");
            for (String materialTypeStr : materialTypes) {
                MaterialType materialType = MaterialType.fromCode(materialTypeStr);
                if (materialType != null) {
                    Function<T, String> extractor = extractorMap.get(materialType);
                    if (extractor != null) {
                        String value = extractor.apply(adm);
                        if (StringUtils.isNotEmpty(value)) {
                            result.put(materialTypeStr, value);
                        }
                    }
                }
            }
        }
        return result;
    }
} 