package com.ximalaya.ad.rdp.parse.response;

import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.proto.RdpProto;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("cmResponseBuilder")
public class CmResponseBuilder implements ResponseBuilder {
    @Override
    public RdpProto.BidResponse build(RdpRequest request, List<CreativeBidInfo> creativeBidInfoList) {
        RdpProto.BidResponse.Builder builder = RdpProto.BidResponse.newBuilder();
        
        return builder.build();
    }

    @Override
    public RdpProto.BidResponse buildNoResponse(RdpRequest request) {
        RdpProto.BidResponse.Builder builder = RdpProto.BidResponse.newBuilder();
        return builder.build();
    }
}
