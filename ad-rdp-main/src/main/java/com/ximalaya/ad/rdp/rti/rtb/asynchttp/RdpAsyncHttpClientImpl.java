package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import com.google.protobuf.TextFormat;
import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.RdpFootballCommonConfig;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.service.SchedulerService;
import com.ximalaya.ad.rdp.service.impl.LoggingServiceImpl;
import com.ximalaya.ad.rdp.stat.ResultCode;
import com.ximalaya.ad.rdp.util.RdpCommonJsonUtils;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class RdpAsyncHttpClientImpl<T, R> implements RdpAsyncHttpClient<T, R> {

    @Autowired
    private LoggingService loggingService;
    
    @Autowired
    private SchedulerService schedulerService;
    
    @Autowired
    private RdpFootballCommonConfig footballCommonConfig;

    private AsyncHttpClient client;
    // ... 其他字段保持不变

    @Override
    public ThirdDspFuture<R> getResponse(RdpRequest rdpRequest,
                                        RequestBuilder requestBuilder,
                                        T requestBody,
                                        ThrowingFunction<byte[], R> responseParser) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }
        
        var request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addParameter("url", request.getUrl())
                        .addParameter("request", body), rdpRequest);

        var handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, List.of(), rdpRequest.getRequestIdNumber(), true, false, false);
        
        var future = client.executeRequest(request, handler);
        var thirdDspFuture = new ThirdDspFuture<R>();
        thirdDspFuture.init(future, List.of());
        return thirdDspFuture;
    }

    @Override
    public ThirdDspFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                                   RequestBuilder requestBuilder,
                                                   T requestBody,
                                                   ThrowingFunction<byte[], R> responseParser,
                                                   List<RequestBidStat> requestBidStats) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }
        
        var request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addLazyParam("dspId", () -> requestBidStats.getFirst().getDspId())
                        .addLazyParam("url", request::getUrl)
                        .addParameter("request", body), rdpRequest);

        var handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, requestBidStats, rdpRequest.getRequestIdNumber(), true, false, false);
        
        setSubmitRequestTime(requestBidStats);
        var future = client.executeRequest(request, handler);
        var thirdDspFuture = new ThirdDspFuture<R>();
        thirdDspFuture.init(future, requestBidStats);
        return thirdDspFuture;
    }

    // ... 其他方法保持不变
}