package com.ximalaya.ad.rdp.rti.asynchttp;

import com.ximalaya.ad.common.util.LogMessageBuilder;
import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.RdpFootballCommonConfig;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.service.LoggingService;
import com.ximalaya.ad.rdp.service.SchedulerService;
import com.ximalaya.ad.rdp.service.impl.LoggingServiceImpl;
import com.ximalaya.ad.rdp.stat.ResultCode;
import com.ximalaya.ad.rdp.util.RdpCommonJsonUtils;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import com.ximalaya.football.client.model.ConfigChangeEvent;
import com.ximalaya.football.client.spring.annotation.FootballConfigChangeListener;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.asynchttpclient.*;
import org.asynchttpclient.cookie.CookieStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ximalaya.ad.common.monitor.AdPrometheusService;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import io.prometheus.client.Gauge;

import java.time.Duration;

/**
 * DspAsyncHttpClientImpl
 *
 * @version 2025/04/07 14:32
 **/

@Slf4j
@Component
public class DspAsyncHttpClientImpl<T, R> implements DspAsyncHttpClient<T, R> {
    private static final Gauge CLIENT_CHANNEL_MONITOR = Gauge.build()
                                                             .name("http_client_channel_monitor")
                                                             .labelNames("host")
                                                             .help("http client monitor")
                                                             .create();
    private static final long MONITOR_VALUE_OFFSET = 100000000;

    private static AsyncHttpClient client;
    private static AsyncHttpClient disabledCookieClient;


    private static final String DSPID = "dspid";
    private static final String REQUEST = "request";
    private static final String RESPONSE = "response";

    @Autowired
    private RdpFootballCommonConfig footballCommonConfig;
    @Autowired
    private SchedulerService schedulerService;
    @Autowired
    private LoggingService loggingService;

    private DspNameResolver dspNameResolver;


    static {
        AdPrometheusService.getInstance().registerCollector(CLIENT_CHANNEL_MONITOR);
        CLIENT_CHANNEL_MONITOR.setChild(new Gauge.Child(){
            public double get() {
                ClientStats clientStats = client.getClientStats();
                long value = clientStats.getTotalActiveConnectionCount() * MONITOR_VALUE_OFFSET + clientStats.getTotalIdleConnectionCount();
                clientStats.getStatsPerHost().forEach((host, hostStats) ->
                        CLIENT_CHANNEL_MONITOR.labels(host).set((double) (hostStats.getHostActiveConnectionCount() * MONITOR_VALUE_OFFSET
                                + hostStats.getHostIdleConnectionCount())));
                return value;
            }
        }, "all");
    }

    @PostConstruct
    public void init() {
        initClient(footballCommonConfig.getDspAsyncHttpClient());
        initDisabledCookieClient(footballCommonConfig.getDspAsyncHttpClient());
        logCookie();
    }

    @FootballConfigChangeListener(value = "rdpCommonConfig")
    public void change(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("dspAsyncHttpClient")) {
            String newValue = changeEvent.getChange("dspAsyncHttpClient").getNewValue();
            DspAsyncHttpClient.DspAsyncHttpClientConfig newConfig = RdpCommonJsonUtils.fromJsonString(DspAsyncHttpClient.DspAsyncHttpClientConfig.class, newValue);
            if (newConfig != null) {
                initClient(newConfig);
                initDisabledCookieClient(footballCommonConfig.getDspAsyncHttpClient());
            }
        }
    }

    private void initClient(DspAsyncHttpClient.DspAsyncHttpClientConfig config) {
        EventLoopGroup eventLoopGroup = Epoll.isAvailable() ? new EpollEventLoopGroup(config.getIoThreads()) : new NioEventLoopGroup(config.getIoThreads());
        dspNameResolver = new DspNameResolver(eventLoopGroup);
        DefaultAsyncHttpClientConfig clientConfig = new DefaultAsyncHttpClientConfig.Builder()
                .setEventLoopGroup(eventLoopGroup)
                .setMaxConnectionsPerHost(config.getMaxConnectionPerDsp())
                .setKeepAlive(true)
                .setFollowRedirect(false)
                .setSoKeepAlive(true)
                .setTcpNoDelay(true)
                .setPooledConnectionIdleTimeout(0)  //连接永远不断开
                .setConnectionTtl(0)
                .build();
        AsyncHttpClient newClient = Dsl.asyncHttpClient(clientConfig);
        AsyncHttpClient oldClient = client;
        client = newClient;
        log.info("AsyncHttpClient init over. EventLoopGroup type: {}", clientConfig.getEventLoopGroup().getClass().getSimpleName());
        if (oldClient != null) {
            try {
                oldClient.close();
            } catch (IOException e) {
                log.error("old AsyncHttpClient close error!", e);
            }
        }
    }

    private void initDisabledCookieClient(DspAsyncHttpClient.DspAsyncHttpClientConfig config) {
        EventLoopGroup eventLoopGroup = Epoll.isAvailable() ? new EpollEventLoopGroup(config.getIoThreads()) : new NioEventLoopGroup(config.getIoThreads());
        dspNameResolver = new DspNameResolver(eventLoopGroup, schedulerService.getDspNameResolverExecutor());
        DefaultAsyncHttpClientConfig clientConfig = new DefaultAsyncHttpClientConfig.Builder()
                .setEventLoopGroup(eventLoopGroup)
                .setMaxConnectionsPerHost(config.getMaxConnectionPerDsp())
                .setUserAgent(config.getUserAgent())
                .setKeepAlive(true)
                .setFollowRedirect(false)
                .setSoKeepAlive(true)
                .setTcpNoDelay(true)
                .setPooledConnectionIdleTimeout(0)  //连接永远不断开
                .setConnectionTtl(0)
                .setCookieStore(new DisabledCookieStore())
                .build();
        AsyncHttpClient newClient = Dsl.asyncHttpClient(clientConfig);
        AsyncHttpClient oldClient = disabledCookieClient;
        disabledCookieClient = newClient;
        log.info("AsyncHttpClient disabled cookie init over. EventLoopGroup type: {}", clientConfig.getEventLoopGroup().getClass().getSimpleName());
        if (oldClient != null) {
            try {
                oldClient.close();
            } catch (IOException e) {
                log.error("old AsyncHttpClient disabled cookie close error!", e);
            }
        }
    }

    private void logCookie() {
        Thread.startVirtualThread(() -> {
            while (true) {
                try {
                    CookieStore cookieStore = client.getConfig().getCookieStore();
                    // NOTE: 有需要可在此log
                    Thread.sleep(Duration.ofMinutes(1));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    @Override
    public RequestBuilder post(String url, DspCommonConfig config) {
        int timeout = config.getSocketTimeout();
        return Dsl.post(url)
                  .setNameResolver(dspNameResolver)
                  .setRequestTimeout(timeout)
                  .setReadTimeout(timeout);
    }

    @Override
    public RdpAsyncFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                                    RequestBuilder requestBuilder,
                                                    T requestBody,
                                                    ThrowingFunction<byte[], R> responseParser,
                                                    List<RequestBidStat> requestBidStats) {
        String body = null;
        if (Objects.nonNull(requestBody)) {
            body = RdpCommonJsonUtils.toJsonString(requestBody);
            requestBuilder.setBody(body);
        }
        Request request = requestBuilder.build();
        loggingService.logCanForDebug(getClass(),
                new LogMessageBuilder("send request")
                        .addLazyParam(DSPID, () -> requestBidStats.getFirst().getDspId())
                        .addLazyParam("url", request::getUrl)
                        .addParameter(REQUEST, body), rdpRequest);

        ProtoAsyncCompletionHandler<R> handler = ProtoAsyncCompletionHandler
                .getInstance(responseParser, requestBidStats, rdpRequest.getRequestIdNumber(), true, false, false);
        setSubmitRequestTime(requestBidStats);
        ListenableFuture<R> future = client.executeRequest(request, handler);
        RdpAsyncFuture<R> rdpAsyncFuture = new RdpAsyncFuture<>();
        rdpAsyncFuture.init(future, requestBidStats);
        return rdpAsyncFuture;
    }

    private void setSubmitRequestTime(List<RequestBidStat> requestBidStats) {
        long submitRequestTime = System.currentTimeMillis();
        requestBidStats.forEach(p -> p.setSubmitDspRequestTime(submitRequestTime));
    }

    static boolean noNeedParseBody(Response response, List<RequestBidStat> requestBidStats, long requestIdNumber, boolean canLog, boolean preRequestResult) {
        if (Objects.isNull(response)) {
            return false;
        }
        int status = response.getStatusCode();
        switch (status) {
            case 200:
                return false;
            case 204:
                requestBidStats.forEach(p -> p.setResult(ResultCode.DSP_REFUSE, preRequestResult));
                return true;
            default:
                requestBidStats.forEach(p -> p.setResult(ResultCode.NOT200OR204));
                String body = "empty";
                try { body = response.getResponseBody(); } catch (Exception ignored) {}
                LoggingServiceImpl.logCanForDebug(DspAsyncHttpClientImpl.class,
                        new LogMessageBuilder("not 200 or 204")
                                .addParameter("responseCode", status)
                                .addParameter(DSPID, requestBidStats.getFirst().getDspId())
                                .addParameter("responseId", requestBidStats.getFirst().getRequestId())
                                .addParameter(RESPONSE, body), canLog, requestIdNumber);
                return true;
        }
    }
}
