package com.ximalaya.ad.rdp.model;

import com.ximalaya.ad.rdp.stat.ResultCode;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * RequestBidStat - DSP请求级别的竞价统计对象
 * 用于管理一次请求中的竞价统计信息
 *
 * @version 2025/04/21 10:52
 **/
@Slf4j
@Data
@Builder
public class RequestBidStat {
    /**
     * 请求ID
     */
    private String requestId;

    /**
     * DSP ID
     */
    private int dspId;

    /**
     * 提交DSP请求的时间，使用volatile确保可见性
     */
    @Builder.Default private volatile long submitDspRequestTime = 0;

    /**
     * 竞价结果
     */
    @Builder.Default private ResultCode result = ResultCode.JUST_RECV;
    @Builder.Default private ResultCode preRequestResult = ResultCode.JUST_RECV;

    /**
     * impId到BidStat的映射，使用ConcurrentHashMap确保线程安全
     * 键：impId
     * 值：该imp对应的BidStat
     */
    private final Map<String, BidStat> impIdToBidStat = new ConcurrentHashMap<>();

    /**
     * 模板ID到imp ID的映射，使用ConcurrentHashMap确保线程安全
     * 键：模板ID
     * 值：该模板对应的imp ID列表
     */
    private final Map<String, String> impToTemplateMap = new ConcurrentHashMap<>();



    public void setResult(ResultCode resultCode, boolean preRequestResult) {
        if (preRequestResult) {
            setPreRequestResult(resultCode);
        } else {
            setResult(resultCode);
        }

        // 将所有BidStat设置为DSP_REFUSE
        if (resultCode.equals(ResultCode.DSP_REFUSE)) {
            impIdToBidStat.values().forEach(bidStat -> bidStat.setResult(ResultCode.DSP_REFUSE));
        }
    }

    public void setDspId(int dspId) {
        this.dspId = dspId;
        impIdToBidStat.values().forEach(bidStat -> bidStat.setDspId(dspId));
    }

    /**
     * 获取指定impId的BidStat
     *
     * @param impId impId
     * @return Optional<BidStat>
     */
    public Optional<BidStat> getBidStat(String impId) {
        return Optional.ofNullable(impIdToBidStat.get(impId));
    }

    /**
     * 获取所有BidStat
     *
     * @return List<BidStat>
     */
    public List<BidStat> getBidStats() {
        return new ArrayList<>(impIdToBidStat.values());
    }

    /**
     * 获取指定模板ID对应的所有impId
     *
     * @param templateId 模板ID
     * @return List<String> impId列表
     */
    public List<String> getImpIdsByTemplateId(String templateId) {
        return new ArrayList<>(impToTemplateMap.entrySet().stream()
                .filter(entry -> entry.getValue().equals(templateId))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList()));
    }

    /**
     * 获取或创建BidStat
     * 如果impId对应的BidStat不存在，则创建一个新的
     *
     * @param impId 曝光ID
     * @return BidStat实例
     */
    public BidStat getOrCreateBidStat(String impId) {
        return impIdToBidStat.computeIfAbsent(impId, k -> BidStat.builder()
                .impId(impId)
                .dspId(this.dspId)
                .build());
    }

    public String getTemplateIdByImpId(String impId) {
        return impToTemplateMap.get(impId);
    }

    /**
     * 批量添加模板ID到imp ID的映射
     *
     * @param mappings 模板ID到imp ID的映射
     */
    public void addAllTemplateToImpMappings(Map<String, String> mappings) {
        if (mappings == null) {
            log.warn("addAllTemplateToImpMappings failed: mappings is null");
            return;
        }
        impToTemplateMap.putAll(mappings);
    }

    /**
     * 根据内部BidStats的状态更新RequestBidStat的状态
     */
    public void updateResultFromBidStats() {
        if (impIdToBidStat.isEmpty()) {
            setResult(ResultCode.DSP_REFUSE, false);
            return;
        }

        // 如果存在DSP_ACCEPT，则设置为DSP_ACCEPT
        List<ResultCode> bidStatResults = impIdToBidStat.values().stream()
                .map(BidStat::getResult)
                .toList();
        if (bidStatResults.stream().anyMatch(result -> result == ResultCode.DSP_ACCEPT)) {
            setResult(ResultCode.DSP_ACCEPT, false);
            return;
        }

        // 其他情况设置为DSP_REFUSE
        setResult(ResultCode.DSP_REFUSE, false);
    }
} 