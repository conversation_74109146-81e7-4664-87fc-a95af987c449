package com.ximalaya.ad.rdp.rti.rtb.config;

import com.ximalaya.football.client.spring.annotation.FootballConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * RdpFootballCommonConfig
 *
 * @version 2025/04/22 14:28
 **/
@FootballConfig("rdpCommonConfig")
@Component
@Data
public class RdpFootballCommonConfig {
    // @FootballField(name = "dspDemandTimeout", defaultValue = "{\"defaultTimeout\":350}")
    // private DspDemandTimeoutConfig dspDemandTimeoutConfig;
}
