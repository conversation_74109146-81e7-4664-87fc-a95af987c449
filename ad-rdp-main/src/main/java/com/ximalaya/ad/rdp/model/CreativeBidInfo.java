package com.ximalaya.ad.rdp.model;

import com.ximalaya.ad.rdp.model.enm.ChargeMode;
import com.ximalaya.ad.rdp.model.enm.PromoGoal;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

@Data
public abstract class CreativeBidInfo<T extends AdCreative> {
    private T adCreative;
    private AdGroup adGroup;
    private Account account;

    private BigDecimal pCtr;     // 预估ctr
    private BigDecimal pCvr;     // 预估cvr
    private BigDecimal pEcpm;    // 预估ecpm
    private BigDecimal bid;     // 最终出价

    public int getAdGroupId() {
        return Optional.ofNullable(adGroup).map(AdGroup::getId).orElse(0);
    }

    // public int getAdCreativeId() {
    //     return Optional.ofNullable(adCreative).map(AdCreative::getId).orElse(0);
    // }

    public PromoGoal getPromoGoal() {
        return Optional.ofNullable(adGroup).map(AdGroup::getGoal).orElse(null);
    }

    public ChargeMode getChargeMode() {
        return Optional.ofNullable(adGroup).map(AdGroup::getMode).orElse(null);
    }

    public abstract BigDecimal getBidPrice();
}
