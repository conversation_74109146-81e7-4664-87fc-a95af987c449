package com.ximalaya.ad.rdp.util;

import com.ximalaya.ad.common.util.AesEncryptUtils;
import com.ximalaya.ad.common.util.EncodeUtils;
import java.nio.charset.StandardCharsets;

public class RdpCommonUtils {

    public static String urlSafeAesEcbPkcs5PaddingEncryptHexString(String content, String key) throws Exception {
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        return EncodeUtils.hexEncode(AesEncryptUtils.aesEcbPkcs5PaddingEncrypt(contentBytes, keyBytes));
    }
} 