package com.ximalaya.ad.rdp.rti.asynchttp;

import com.ximalaya.ad.rdp.model.RequestBidStat;
import com.ximalaya.ad.rdp.rti.rtb.config.dsp.DspCommonConfig;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.Data;
import org.asynchttpclient.RequestBuilder;
import org.asynchttpclient.netty.channel.DefaultChannelPool;

import java.util.List;

/**
 * RdpAsyncHttpClient - RDP系统异步HTTP客户端
 * 支持RTB、RTA等多种场景的HTTP通信
 *
 * @version 2025/04/21 15:00
 * @param <T> 请求内容类型
 * @param <R> 响应内容类型
 */
public interface RdpAsyncHttpClient<T, R> {

    @Data
    class RdpAsyncHttpClientConfig {
        private int ioThreads;
        private int maxConnectionPerDsp;
        private int initConnectionPerDsp;
        private String userAgent;
        private DefaultChannelPool.PoolLeaseStrategy poolLeaseStrategy;
    }

    /**
     * 创建POST请求构建器
     */
    RequestBuilder post(String url, DspCommonConfig config);

    /**
     * 通用的异步HTTP请求方法
     * @param rdpRequest RDP请求对象
     * @param requestBuilder 请求构建器
     * @param requestBody 请求体
     * @param responseParser 响应解析器
     * @return ThirdDspFuture 异步响应future
     */
    RdpAsyncFuture<R> getResponse(RdpRequest rdpRequest,
                                  RequestBuilder requestBuilder,
                                  T requestBody,
                                  ThrowingFunction<byte[], R> responseParser);

    /**
     * RTB场景的请求方法（带统计）
     * @param rdpRequest RDP请求对象
     * @param requestBuilder 请求构建器
     * @param requestBody 请求体
     * @param responseParser 响应解析器
     * @param requestBidStats 请求出价统计列表
     * @return ThirdDspFuture 异步响应future
     */
    default RdpAsyncFuture<R> getJsonToProtoResponse(RdpRequest rdpRequest,
                                                     RequestBuilder requestBuilder,
                                                     T requestBody,
                                                     ThrowingFunction<byte[], R> responseParser,
                                                     List<RequestBidStat> requestBidStats) {
        var future = getResponse(rdpRequest, requestBuilder, requestBody, responseParser);
        // 设置统计信息
        if (requestBidStats != null && !requestBidStats.isEmpty()) {
            future.setRequestBidStats(requestBidStats);
        }
        return future;
    }
}