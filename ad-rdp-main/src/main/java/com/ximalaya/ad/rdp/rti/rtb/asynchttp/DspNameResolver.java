package com.ximalaya.ad.rdp.rti.rtb.asynchttp;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.netty.channel.EventLoop;
import io.netty.channel.EventLoopGroup;
import io.netty.resolver.NameResolver;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;
import io.netty.util.internal.SocketUtils;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * RDP 域名解析器实现
 * 提供 DNS 解析缓存和异步解析功能
 */
@Slf4j
public class DspNameResolver implements NameResolver<InetAddress> {

    private LoadingCache<String, List<InetAddress>> nameCache;
    private EventLoopGroup eventExecutors;
    private ThreadPoolExecutor threadPoolExecutor;

    private static final int CONCURRENT = 4;

    /**
     * 构造函数
     *
     * @param eventLoopGroup 事件循环组
     * @param threadPoolExecutor 线程池执行器
     */
    public DspNameResolver(EventLoopGroup eventLoopGroup, ThreadPoolExecutor threadPoolExecutor) {
        this.eventExecutors = eventLoopGroup;
        this.threadPoolExecutor = threadPoolExecutor;
        init();
    }

    /**
     * 初始化缓存
     */
    private void init() {
        nameCache = CacheBuilder.newBuilder()
                                .refreshAfterWrite(1, TimeUnit.HOURS)
                                .concurrencyLevel(CONCURRENT)
                                .recordStats()
                                .build(new CacheLoader<String, List<InetAddress>>() {
                                    @Override
                                    public List<InetAddress> load(String key) throws Exception {
                                        return Lists.newArrayList(SocketUtils.allAddressesByName(key));
                                    }
                                });
    }

    @Override
    public Future<InetAddress> resolve(String s) {
        EventLoop eventLoop = eventExecutors.next();
        Promise<InetAddress> promise = eventLoop.newPromise();
        threadPoolExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s).get(0));
            } catch (Throwable e) {
                promise.setFailure(e);
            }
        });
        return promise;
    }

    @Override
    public Future<InetAddress> resolve(String s, Promise<InetAddress> promise) {
        threadPoolExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s).get(0));
            } catch (Throwable e) {
                promise.setFailure(e);
            }
        });
        return promise;
    }

    @Override
    public Future<List<InetAddress>> resolveAll(String s) {
        EventLoop eventLoop = eventExecutors.next();
        Promise<List<InetAddress>> promise = eventLoop.newPromise();
        threadPoolExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s));
            } catch (Throwable e) {
                promise.setFailure(e);
            }
        });
        return promise;
    }

    @Override
    public Future<List<InetAddress>> resolveAll(String s, Promise<List<InetAddress>> promise) {
        threadPoolExecutor.execute(() -> {
            try {
                promise.setSuccess(nameCache.get(s));
            } catch (Throwable e) {
                promise.setFailure(e);
            }
        });
        return promise;
    }

    /**
     * 预解析域名
     *
     * @param s 域名
     */
    public void preResolveAll(String s) {
        threadPoolExecutor.execute(() -> {
            try {
                nameCache.get(s);
            } catch (Throwable ignored) {
                // 忽略预解析错误
            }
        });
    }

    /**
     * 立即解析域名
     *
     * @param s 域名
     * @return IP 地址列表
     */
    public List<InetAddress> resolveAllImmediately(String s) {
        try {
            return nameCache.get(s);
        } catch (Throwable e) {
            log.error("Failed to resolve domain: {}", s, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void close() {
        if (eventExecutors != null) {
            eventExecutors.shutdownGracefully();
        }
    }
}
