package com.ximalaya.ad.rdp.model.rta;

import com.ximalaya.ad.rdp.model.Account;
import com.ximalaya.ad.rdp.model.AdCreative;
import com.ximalaya.ad.rdp.model.AdGroup;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.model.enm.RtaType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

@Data
public class RtaCreativeBidInfo extends CreativeBidInfo<AdCreative> {

    @Override
    public BigDecimal getBidPrice() {
        return Optional.ofNullable(adGroup).map(AdGroup::getBid).orElse(BigDecimal.ZERO);
    }

    public RtaType getRtaType() {
        return Optional.ofNullable(account).map(Account::getRtaType).orElse(null);
    }
} 