package com.ximalaya.ad.rdp.rti.rtb.config.dsp;

import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DspCommonConfig DSP通用配置
 *
 * @version 2025/04/23 17:37
 **/
@Data
public class DspCommonConfig<T extends DspCommonConfig.TemplateConfig> {
    private String requestUrl;
    private int connectTimeout = 0;
    private int socketTimeout = 0;

    /**
     * 获取超时时间,单位毫秒
     * @param rdpRequest
     * @return
     */
    public long getTimeout(RdpRequest rdpRequest) {
        return rdpRequest.getBidRequest().getTimeout();
    }

    protected Map<String, List<T>> templateConfigMap;   // 模板映射，目前与广告位无关

    public void setTemplates(List<T> templates) {
        templateConfigMap = templates.stream().collect(Collectors.groupingBy(TemplateConfig::getTemplateName));
    }
    public List<T> getTemplateConfig(String templateName) {
        return templateConfigMap.getOrDefault(templateName, null);
    }

    @Data
    public static class TemplateConfig {
        private String templateName;

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        }
    }
}
