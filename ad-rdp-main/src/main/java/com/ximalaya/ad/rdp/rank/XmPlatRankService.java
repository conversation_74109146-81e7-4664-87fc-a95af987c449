package com.ximalaya.ad.rdp.rank;

import com.ximalaya.ad.rdp.model.AdGroup;
import com.ximalaya.ad.rdp.model.CreativeBidInfo;
import com.ximalaya.ad.rdp.vo.request.RdpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("xmPlatRankService")
public class XmPlatRankService implements RankService {
    @Override
    public List<CreativeBidInfo> rank(RdpRequest request, List<CreativeBidInfo> creativeBidInfos) {
        return List.of();
    }
}
