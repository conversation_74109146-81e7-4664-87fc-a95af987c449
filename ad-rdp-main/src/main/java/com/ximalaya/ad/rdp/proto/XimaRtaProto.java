// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ximalaya-rta.proto

// Protobuf Java Version: 3.25.5
package com.ximalaya.ad.rdp.proto;

public final class XimaRtaProto {
  private XimaRtaProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code com.ximalaya.ad.rdp.proto.OsType}
   */
  public enum OsType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未知类型
     * </pre>
     *
     * <code>UNKNOWN_OS = 0;</code>
     */
    UNKNOWN_OS(0),
    /**
     * <pre>
     * Android
     * </pre>
     *
     * <code>ANDROID = 1;</code>
     */
    ANDROID(1),
    /**
     * <pre>
     * iOS
     * </pre>
     *
     * <code>IOS = 2;</code>
     */
    IOS(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 未知类型
     * </pre>
     *
     * <code>UNKNOWN_OS = 0;</code>
     */
    public static final int UNKNOWN_OS_VALUE = 0;
    /**
     * <pre>
     * Android
     * </pre>
     *
     * <code>ANDROID = 1;</code>
     */
    public static final int ANDROID_VALUE = 1;
    /**
     * <pre>
     * iOS
     * </pre>
     *
     * <code>IOS = 2;</code>
     */
    public static final int IOS_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static OsType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static OsType forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN_OS;
        case 1: return ANDROID;
        case 2: return IOS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<OsType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        OsType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<OsType>() {
            public OsType findValueByNumber(int number) {
              return OsType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final OsType[] VALUES = values();

    public static OsType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private OsType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:com.ximalaya.ad.rdp.proto.OsType)
  }

  public interface ReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.ximalaya.ad.rdp.proto.Req)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <pre>
     * 请求时间
     * </pre>
     *
     * <code>int64 request_time = 2;</code>
     * @return The requestTime.
     */
    long getRequestTime();

    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @return A list containing the rtaIds.
     */
    java.util.List<java.lang.String>
        getRtaIdsList();
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @return The count of rtaIds.
     */
    int getRtaIdsCount();
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @param index The index of the element to return.
     * @return The rtaIds at the given index.
     */
    java.lang.String getRtaIds(int index);
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the rtaIds at the given index.
     */
    com.google.protobuf.ByteString
        getRtaIdsBytes(int index);

    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
     * @return The enum numeric value on the wire for osType.
     */
    int getOsTypeValue();
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
     * @return The osType.
     */
    com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType getOsType();

    /**
     * <pre>
     * 设备imei md5
     * </pre>
     *
     * <code>string imei_md5 = 5;</code>
     * @return The imeiMd5.
     */
    java.lang.String getImeiMd5();
    /**
     * <pre>
     * 设备imei md5
     * </pre>
     *
     * <code>string imei_md5 = 5;</code>
     * @return The bytes for imeiMd5.
     */
    com.google.protobuf.ByteString
        getImeiMd5Bytes();

    /**
     * <pre>
     * 设备imei
     * </pre>
     *
     * <code>string imei = 6;</code>
     * @return The imei.
     */
    java.lang.String getImei();
    /**
     * <pre>
     * 设备imei
     * </pre>
     *
     * <code>string imei = 6;</code>
     * @return The bytes for imei.
     */
    com.google.protobuf.ByteString
        getImeiBytes();

    /**
     * <pre>
     * 设备oaid md5
     * </pre>
     *
     * <code>string oaid_md5 = 7;</code>
     * @return The oaidMd5.
     */
    java.lang.String getOaidMd5();
    /**
     * <pre>
     * 设备oaid md5
     * </pre>
     *
     * <code>string oaid_md5 = 7;</code>
     * @return The bytes for oaidMd5.
     */
    com.google.protobuf.ByteString
        getOaidMd5Bytes();

    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 8;</code>
     * @return The oaid.
     */
    java.lang.String getOaid();
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 8;</code>
     * @return The bytes for oaid.
     */
    com.google.protobuf.ByteString
        getOaidBytes();

    /**
     * <pre>
     * 设备idfa md5
     * </pre>
     *
     * <code>string idfa_md5 = 9;</code>
     * @return The idfaMd5.
     */
    java.lang.String getIdfaMd5();
    /**
     * <pre>
     * 设备idfa md5
     * </pre>
     *
     * <code>string idfa_md5 = 9;</code>
     * @return The bytes for idfaMd5.
     */
    com.google.protobuf.ByteString
        getIdfaMd5Bytes();

    /**
     * <pre>
     * 设备idfa
     * </pre>
     *
     * <code>string idfa = 10;</code>
     * @return The idfa.
     */
    java.lang.String getIdfa();
    /**
     * <pre>
     * 设备idfa
     * </pre>
     *
     * <code>string idfa = 10;</code>
     * @return The bytes for idfa.
     */
    com.google.protobuf.ByteString
        getIdfaBytes();

    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 11;</code>
     * @return The customInfo.
     */
    java.lang.String getCustomInfo();
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 11;</code>
     * @return The bytes for customInfo.
     */
    com.google.protobuf.ByteString
        getCustomInfoBytes();

    /**
     * <pre>
     * 手机品牌
     * </pre>
     *
     * <code>string brand = 12;</code>
     * @return The brand.
     */
    java.lang.String getBrand();
    /**
     * <pre>
     * 手机品牌
     * </pre>
     *
     * <code>string brand = 12;</code>
     * @return The bytes for brand.
     */
    com.google.protobuf.ByteString
        getBrandBytes();

    /**
     * <pre>
     * 渠道
     * </pre>
     *
     * <code>string channel = 13;</code>
     * @return The channel.
     */
    java.lang.String getChannel();
    /**
     * <pre>
     * 渠道
     * </pre>
     *
     * <code>string channel = 13;</code>
     * @return The bytes for channel.
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    /**
     * <pre>
     * md5(id+request_time+channel+secretKey)
     * </pre>
     *
     * <code>string sign = 14;</code>
     * @return The sign.
     */
    java.lang.String getSign();
    /**
     * <pre>
     * md5(id+request_time+channel+secretKey)
     * </pre>
     *
     * <code>string sign = 14;</code>
     * @return The bytes for sign.
     */
    com.google.protobuf.ByteString
        getSignBytes();
  }
  /**
   * Protobuf type {@code com.ximalaya.ad.rdp.proto.Req}
   */
  public static final class Req extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.ximalaya.ad.rdp.proto.Req)
      ReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Req.newBuilder() to construct.
    private Req(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Req() {
      id_ = "";
      rtaIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      osType_ = 0;
      imeiMd5_ = "";
      imei_ = "";
      oaidMd5_ = "";
      oaid_ = "";
      idfaMd5_ = "";
      idfa_ = "";
      customInfo_ = "";
      brand_ = "";
      channel_ = "";
      sign_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Req();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Req_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object id_ = "";
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQUEST_TIME_FIELD_NUMBER = 2;
    private long requestTime_ = 0L;
    /**
     * <pre>
     * 请求时间
     * </pre>
     *
     * <code>int64 request_time = 2;</code>
     * @return The requestTime.
     */
    @java.lang.Override
    public long getRequestTime() {
      return requestTime_;
    }

    public static final int RTA_IDS_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList rtaIds_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @return A list containing the rtaIds.
     */
    public com.google.protobuf.ProtocolStringList
        getRtaIdsList() {
      return rtaIds_;
    }
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @return The count of rtaIds.
     */
    public int getRtaIdsCount() {
      return rtaIds_.size();
    }
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @param index The index of the element to return.
     * @return The rtaIds at the given index.
     */
    public java.lang.String getRtaIds(int index) {
      return rtaIds_.get(index);
    }
    /**
     * <pre>
     * 本次请求的rta_id列表
     * </pre>
     *
     * <code>repeated string rta_ids = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the rtaIds at the given index.
     */
    public com.google.protobuf.ByteString
        getRtaIdsBytes(int index) {
      return rtaIds_.getByteString(index);
    }

    public static final int OS_TYPE_FIELD_NUMBER = 4;
    private int osType_ = 0;
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
     * @return The enum numeric value on the wire for osType.
     */
    @java.lang.Override public int getOsTypeValue() {
      return osType_;
    }
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
     * @return The osType.
     */
    @java.lang.Override public com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType getOsType() {
      com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType result = com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.forNumber(osType_);
      return result == null ? com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.UNRECOGNIZED : result;
    }

    public static final int IMEI_MD5_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object imeiMd5_ = "";
    /**
     * <pre>
     * 设备imei md5
     * </pre>
     *
     * <code>string imei_md5 = 5;</code>
     * @return The imeiMd5.
     */
    @java.lang.Override
    public java.lang.String getImeiMd5() {
      java.lang.Object ref = imeiMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        imeiMd5_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备imei md5
     * </pre>
     *
     * <code>string imei_md5 = 5;</code>
     * @return The bytes for imeiMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getImeiMd5Bytes() {
      java.lang.Object ref = imeiMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        imeiMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IMEI_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object imei_ = "";
    /**
     * <pre>
     * 设备imei
     * </pre>
     *
     * <code>string imei = 6;</code>
     * @return The imei.
     */
    @java.lang.Override
    public java.lang.String getImei() {
      java.lang.Object ref = imei_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        imei_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备imei
     * </pre>
     *
     * <code>string imei = 6;</code>
     * @return The bytes for imei.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getImeiBytes() {
      java.lang.Object ref = imei_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        imei_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OAID_MD5_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object oaidMd5_ = "";
    /**
     * <pre>
     * 设备oaid md5
     * </pre>
     *
     * <code>string oaid_md5 = 7;</code>
     * @return The oaidMd5.
     */
    @java.lang.Override
    public java.lang.String getOaidMd5() {
      java.lang.Object ref = oaidMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        oaidMd5_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备oaid md5
     * </pre>
     *
     * <code>string oaid_md5 = 7;</code>
     * @return The bytes for oaidMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOaidMd5Bytes() {
      java.lang.Object ref = oaidMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        oaidMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OAID_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object oaid_ = "";
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 8;</code>
     * @return The oaid.
     */
    @java.lang.Override
    public java.lang.String getOaid() {
      java.lang.Object ref = oaid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        oaid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 8;</code>
     * @return The bytes for oaid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOaidBytes() {
      java.lang.Object ref = oaid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        oaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IDFA_MD5_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object idfaMd5_ = "";
    /**
     * <pre>
     * 设备idfa md5
     * </pre>
     *
     * <code>string idfa_md5 = 9;</code>
     * @return The idfaMd5.
     */
    @java.lang.Override
    public java.lang.String getIdfaMd5() {
      java.lang.Object ref = idfaMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idfaMd5_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备idfa md5
     * </pre>
     *
     * <code>string idfa_md5 = 9;</code>
     * @return The bytes for idfaMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdfaMd5Bytes() {
      java.lang.Object ref = idfaMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idfaMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IDFA_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object idfa_ = "";
    /**
     * <pre>
     * 设备idfa
     * </pre>
     *
     * <code>string idfa = 10;</code>
     * @return The idfa.
     */
    @java.lang.Override
    public java.lang.String getIdfa() {
      java.lang.Object ref = idfa_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idfa_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备idfa
     * </pre>
     *
     * <code>string idfa = 10;</code>
     * @return The bytes for idfa.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdfaBytes() {
      java.lang.Object ref = idfa_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idfa_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CUSTOM_INFO_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object customInfo_ = "";
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 11;</code>
     * @return The customInfo.
     */
    @java.lang.Override
    public java.lang.String getCustomInfo() {
      java.lang.Object ref = customInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        customInfo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 11;</code>
     * @return The bytes for customInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCustomInfoBytes() {
      java.lang.Object ref = customInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        customInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BRAND_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object brand_ = "";
    /**
     * <pre>
     * 手机品牌
     * </pre>
     *
     * <code>string brand = 12;</code>
     * @return The brand.
     */
    @java.lang.Override
    public java.lang.String getBrand() {
      java.lang.Object ref = brand_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        brand_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 手机品牌
     * </pre>
     *
     * <code>string brand = 12;</code>
     * @return The bytes for brand.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBrandBytes() {
      java.lang.Object ref = brand_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        brand_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHANNEL_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object channel_ = "";
    /**
     * <pre>
     * 渠道
     * </pre>
     *
     * <code>string channel = 13;</code>
     * @return The channel.
     */
    @java.lang.Override
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 渠道
     * </pre>
     *
     * <code>string channel = 13;</code>
     * @return The bytes for channel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SIGN_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile java.lang.Object sign_ = "";
    /**
     * <pre>
     * md5(id+request_time+channel+secretKey)
     * </pre>
     *
     * <code>string sign = 14;</code>
     * @return The sign.
     */
    @java.lang.Override
    public java.lang.String getSign() {
      java.lang.Object ref = sign_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sign_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * md5(id+request_time+channel+secretKey)
     * </pre>
     *
     * <code>string sign = 14;</code>
     * @return The bytes for sign.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSignBytes() {
      java.lang.Object ref = sign_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (requestTime_ != 0L) {
        output.writeInt64(2, requestTime_);
      }
      for (int i = 0; i < rtaIds_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, rtaIds_.getRaw(i));
      }
      if (osType_ != com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.UNKNOWN_OS.getNumber()) {
        output.writeEnum(4, osType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(imeiMd5_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, imeiMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(imei_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, imei_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(oaidMd5_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, oaidMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(oaid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, oaid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(idfaMd5_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, idfaMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(idfa_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, idfa_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customInfo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, customInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(brand_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, brand_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(channel_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, channel_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sign_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, sign_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (requestTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, requestTime_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rtaIds_.size(); i++) {
          dataSize += computeStringSizeNoTag(rtaIds_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getRtaIdsList().size();
      }
      if (osType_ != com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.UNKNOWN_OS.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, osType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(imeiMd5_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, imeiMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(imei_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, imei_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(oaidMd5_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, oaidMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(oaid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, oaid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(idfaMd5_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, idfaMd5_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(idfa_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, idfa_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customInfo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, customInfo_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(brand_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, brand_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(channel_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, channel_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sign_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, sign_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.Req)) {
        return super.equals(obj);
      }
      com.ximalaya.ad.rdp.proto.XimaRtaProto.Req other = (com.ximalaya.ad.rdp.proto.XimaRtaProto.Req) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (getRequestTime()
          != other.getRequestTime()) return false;
      if (!getRtaIdsList()
          .equals(other.getRtaIdsList())) return false;
      if (osType_ != other.osType_) return false;
      if (!getImeiMd5()
          .equals(other.getImeiMd5())) return false;
      if (!getImei()
          .equals(other.getImei())) return false;
      if (!getOaidMd5()
          .equals(other.getOaidMd5())) return false;
      if (!getOaid()
          .equals(other.getOaid())) return false;
      if (!getIdfaMd5()
          .equals(other.getIdfaMd5())) return false;
      if (!getIdfa()
          .equals(other.getIdfa())) return false;
      if (!getCustomInfo()
          .equals(other.getCustomInfo())) return false;
      if (!getBrand()
          .equals(other.getBrand())) return false;
      if (!getChannel()
          .equals(other.getChannel())) return false;
      if (!getSign()
          .equals(other.getSign())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + REQUEST_TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestTime());
      if (getRtaIdsCount() > 0) {
        hash = (37 * hash) + RTA_IDS_FIELD_NUMBER;
        hash = (53 * hash) + getRtaIdsList().hashCode();
      }
      hash = (37 * hash) + OS_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + osType_;
      hash = (37 * hash) + IMEI_MD5_FIELD_NUMBER;
      hash = (53 * hash) + getImeiMd5().hashCode();
      hash = (37 * hash) + IMEI_FIELD_NUMBER;
      hash = (53 * hash) + getImei().hashCode();
      hash = (37 * hash) + OAID_MD5_FIELD_NUMBER;
      hash = (53 * hash) + getOaidMd5().hashCode();
      hash = (37 * hash) + OAID_FIELD_NUMBER;
      hash = (53 * hash) + getOaid().hashCode();
      hash = (37 * hash) + IDFA_MD5_FIELD_NUMBER;
      hash = (53 * hash) + getIdfaMd5().hashCode();
      hash = (37 * hash) + IDFA_FIELD_NUMBER;
      hash = (53 * hash) + getIdfa().hashCode();
      hash = (37 * hash) + CUSTOM_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getCustomInfo().hashCode();
      hash = (37 * hash) + BRAND_FIELD_NUMBER;
      hash = (53 * hash) + getBrand().hashCode();
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel().hashCode();
      hash = (37 * hash) + SIGN_FIELD_NUMBER;
      hash = (53 * hash) + getSign().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.ximalaya.ad.rdp.proto.XimaRtaProto.Req prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.ximalaya.ad.rdp.proto.Req}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.ximalaya.ad.rdp.proto.Req)
        com.ximalaya.ad.rdp.proto.XimaRtaProto.ReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Req_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.Builder.class);
      }

      // Construct using com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        requestTime_ = 0L;
        rtaIds_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        osType_ = 0;
        imeiMd5_ = "";
        imei_ = "";
        oaidMd5_ = "";
        oaid_ = "";
        idfaMd5_ = "";
        idfa_ = "";
        customInfo_ = "";
        brand_ = "";
        channel_ = "";
        sign_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Req getDefaultInstanceForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.getDefaultInstance();
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Req build() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.Req result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Req buildPartial() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.Req result = new com.ximalaya.ad.rdp.proto.XimaRtaProto.Req(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.ximalaya.ad.rdp.proto.XimaRtaProto.Req result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.requestTime_ = requestTime_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          rtaIds_.makeImmutable();
          result.rtaIds_ = rtaIds_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.osType_ = osType_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.imeiMd5_ = imeiMd5_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.imei_ = imei_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.oaidMd5_ = oaidMd5_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.oaid_ = oaid_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.idfaMd5_ = idfaMd5_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.idfa_ = idfa_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.customInfo_ = customInfo_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.brand_ = brand_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.channel_ = channel_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.sign_ = sign_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.Req) {
          return mergeFrom((com.ximalaya.ad.rdp.proto.XimaRtaProto.Req)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.ximalaya.ad.rdp.proto.XimaRtaProto.Req other) {
        if (other == com.ximalaya.ad.rdp.proto.XimaRtaProto.Req.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getRequestTime() != 0L) {
          setRequestTime(other.getRequestTime());
        }
        if (!other.rtaIds_.isEmpty()) {
          if (rtaIds_.isEmpty()) {
            rtaIds_ = other.rtaIds_;
            bitField0_ |= 0x00000004;
          } else {
            ensureRtaIdsIsMutable();
            rtaIds_.addAll(other.rtaIds_);
          }
          onChanged();
        }
        if (other.osType_ != 0) {
          setOsTypeValue(other.getOsTypeValue());
        }
        if (!other.getImeiMd5().isEmpty()) {
          imeiMd5_ = other.imeiMd5_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getImei().isEmpty()) {
          imei_ = other.imei_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getOaidMd5().isEmpty()) {
          oaidMd5_ = other.oaidMd5_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (!other.getOaid().isEmpty()) {
          oaid_ = other.oaid_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (!other.getIdfaMd5().isEmpty()) {
          idfaMd5_ = other.idfaMd5_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (!other.getIdfa().isEmpty()) {
          idfa_ = other.idfa_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (!other.getCustomInfo().isEmpty()) {
          customInfo_ = other.customInfo_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (!other.getBrand().isEmpty()) {
          brand_ = other.brand_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (!other.getChannel().isEmpty()) {
          channel_ = other.channel_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (!other.getSign().isEmpty()) {
          sign_ = other.sign_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                requestTime_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                java.lang.String s = input.readStringRequireUtf8();
                ensureRtaIdsIsMutable();
                rtaIds_.add(s);
                break;
              } // case 26
              case 32: {
                osType_ = input.readEnum();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                imeiMd5_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                imei_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                oaidMd5_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                oaid_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                idfaMd5_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                idfa_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                customInfo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                brand_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                channel_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                sign_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object id_ = "";
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The id.
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private long requestTime_ ;
      /**
       * <pre>
       * 请求时间
       * </pre>
       *
       * <code>int64 request_time = 2;</code>
       * @return The requestTime.
       */
      @java.lang.Override
      public long getRequestTime() {
        return requestTime_;
      }
      /**
       * <pre>
       * 请求时间
       * </pre>
       *
       * <code>int64 request_time = 2;</code>
       * @param value The requestTime to set.
       * @return This builder for chaining.
       */
      public Builder setRequestTime(long value) {

        requestTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求时间
       * </pre>
       *
       * <code>int64 request_time = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRequestTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        requestTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList rtaIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureRtaIdsIsMutable() {
        if (!rtaIds_.isModifiable()) {
          rtaIds_ = new com.google.protobuf.LazyStringArrayList(rtaIds_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @return A list containing the rtaIds.
       */
      public com.google.protobuf.ProtocolStringList
          getRtaIdsList() {
        rtaIds_.makeImmutable();
        return rtaIds_;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @return The count of rtaIds.
       */
      public int getRtaIdsCount() {
        return rtaIds_.size();
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param index The index of the element to return.
       * @return The rtaIds at the given index.
       */
      public java.lang.String getRtaIds(int index) {
        return rtaIds_.get(index);
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param index The index of the value to return.
       * @return The bytes of the rtaIds at the given index.
       */
      public com.google.protobuf.ByteString
          getRtaIdsBytes(int index) {
        return rtaIds_.getByteString(index);
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param index The index to set the value at.
       * @param value The rtaIds to set.
       * @return This builder for chaining.
       */
      public Builder setRtaIds(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRtaIdsIsMutable();
        rtaIds_.set(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param value The rtaIds to add.
       * @return This builder for chaining.
       */
      public Builder addRtaIds(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRtaIdsIsMutable();
        rtaIds_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param values The rtaIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRtaIds(
          java.lang.Iterable<java.lang.String> values) {
        ensureRtaIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rtaIds_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRtaIds() {
        rtaIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次请求的rta_id列表
       * </pre>
       *
       * <code>repeated string rta_ids = 3;</code>
       * @param value The bytes of the rtaIds to add.
       * @return This builder for chaining.
       */
      public Builder addRtaIdsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        ensureRtaIdsIsMutable();
        rtaIds_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private int osType_ = 0;
      /**
       * <pre>
       * 系统
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
       * @return The enum numeric value on the wire for osType.
       */
      @java.lang.Override public int getOsTypeValue() {
        return osType_;
      }
      /**
       * <pre>
       * 系统
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
       * @param value The enum numeric value on the wire for osType to set.
       * @return This builder for chaining.
       */
      public Builder setOsTypeValue(int value) {
        osType_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
       * @return The osType.
       */
      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType getOsType() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType result = com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.forNumber(osType_);
        return result == null ? com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 系统
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
       * @param value The osType to set.
       * @return This builder for chaining.
       */
      public Builder setOsType(com.ximalaya.ad.rdp.proto.XimaRtaProto.OsType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        osType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 系统
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.OsType os_type = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        osType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object imeiMd5_ = "";
      /**
       * <pre>
       * 设备imei md5
       * </pre>
       *
       * <code>string imei_md5 = 5;</code>
       * @return The imeiMd5.
       */
      public java.lang.String getImeiMd5() {
        java.lang.Object ref = imeiMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          imeiMd5_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备imei md5
       * </pre>
       *
       * <code>string imei_md5 = 5;</code>
       * @return The bytes for imeiMd5.
       */
      public com.google.protobuf.ByteString
          getImeiMd5Bytes() {
        java.lang.Object ref = imeiMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          imeiMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备imei md5
       * </pre>
       *
       * <code>string imei_md5 = 5;</code>
       * @param value The imeiMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setImeiMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        imeiMd5_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备imei md5
       * </pre>
       *
       * <code>string imei_md5 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearImeiMd5() {
        imeiMd5_ = getDefaultInstance().getImeiMd5();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备imei md5
       * </pre>
       *
       * <code>string imei_md5 = 5;</code>
       * @param value The bytes for imeiMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setImeiMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        imeiMd5_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object imei_ = "";
      /**
       * <pre>
       * 设备imei
       * </pre>
       *
       * <code>string imei = 6;</code>
       * @return The imei.
       */
      public java.lang.String getImei() {
        java.lang.Object ref = imei_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          imei_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备imei
       * </pre>
       *
       * <code>string imei = 6;</code>
       * @return The bytes for imei.
       */
      public com.google.protobuf.ByteString
          getImeiBytes() {
        java.lang.Object ref = imei_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          imei_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备imei
       * </pre>
       *
       * <code>string imei = 6;</code>
       * @param value The imei to set.
       * @return This builder for chaining.
       */
      public Builder setImei(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        imei_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备imei
       * </pre>
       *
       * <code>string imei = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearImei() {
        imei_ = getDefaultInstance().getImei();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备imei
       * </pre>
       *
       * <code>string imei = 6;</code>
       * @param value The bytes for imei to set.
       * @return This builder for chaining.
       */
      public Builder setImeiBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        imei_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object oaidMd5_ = "";
      /**
       * <pre>
       * 设备oaid md5
       * </pre>
       *
       * <code>string oaid_md5 = 7;</code>
       * @return The oaidMd5.
       */
      public java.lang.String getOaidMd5() {
        java.lang.Object ref = oaidMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          oaidMd5_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备oaid md5
       * </pre>
       *
       * <code>string oaid_md5 = 7;</code>
       * @return The bytes for oaidMd5.
       */
      public com.google.protobuf.ByteString
          getOaidMd5Bytes() {
        java.lang.Object ref = oaidMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          oaidMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备oaid md5
       * </pre>
       *
       * <code>string oaid_md5 = 7;</code>
       * @param value The oaidMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setOaidMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        oaidMd5_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备oaid md5
       * </pre>
       *
       * <code>string oaid_md5 = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOaidMd5() {
        oaidMd5_ = getDefaultInstance().getOaidMd5();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备oaid md5
       * </pre>
       *
       * <code>string oaid_md5 = 7;</code>
       * @param value The bytes for oaidMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setOaidMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        oaidMd5_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object oaid_ = "";
      /**
       * <pre>
       * 设备oaid
       * </pre>
       *
       * <code>string oaid = 8;</code>
       * @return The oaid.
       */
      public java.lang.String getOaid() {
        java.lang.Object ref = oaid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          oaid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备oaid
       * </pre>
       *
       * <code>string oaid = 8;</code>
       * @return The bytes for oaid.
       */
      public com.google.protobuf.ByteString
          getOaidBytes() {
        java.lang.Object ref = oaid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          oaid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备oaid
       * </pre>
       *
       * <code>string oaid = 8;</code>
       * @param value The oaid to set.
       * @return This builder for chaining.
       */
      public Builder setOaid(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        oaid_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备oaid
       * </pre>
       *
       * <code>string oaid = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearOaid() {
        oaid_ = getDefaultInstance().getOaid();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备oaid
       * </pre>
       *
       * <code>string oaid = 8;</code>
       * @param value The bytes for oaid to set.
       * @return This builder for chaining.
       */
      public Builder setOaidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        oaid_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object idfaMd5_ = "";
      /**
       * <pre>
       * 设备idfa md5
       * </pre>
       *
       * <code>string idfa_md5 = 9;</code>
       * @return The idfaMd5.
       */
      public java.lang.String getIdfaMd5() {
        java.lang.Object ref = idfaMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          idfaMd5_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备idfa md5
       * </pre>
       *
       * <code>string idfa_md5 = 9;</code>
       * @return The bytes for idfaMd5.
       */
      public com.google.protobuf.ByteString
          getIdfaMd5Bytes() {
        java.lang.Object ref = idfaMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idfaMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备idfa md5
       * </pre>
       *
       * <code>string idfa_md5 = 9;</code>
       * @param value The idfaMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setIdfaMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        idfaMd5_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备idfa md5
       * </pre>
       *
       * <code>string idfa_md5 = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdfaMd5() {
        idfaMd5_ = getDefaultInstance().getIdfaMd5();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备idfa md5
       * </pre>
       *
       * <code>string idfa_md5 = 9;</code>
       * @param value The bytes for idfaMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setIdfaMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        idfaMd5_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object idfa_ = "";
      /**
       * <pre>
       * 设备idfa
       * </pre>
       *
       * <code>string idfa = 10;</code>
       * @return The idfa.
       */
      public java.lang.String getIdfa() {
        java.lang.Object ref = idfa_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          idfa_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备idfa
       * </pre>
       *
       * <code>string idfa = 10;</code>
       * @return The bytes for idfa.
       */
      public com.google.protobuf.ByteString
          getIdfaBytes() {
        java.lang.Object ref = idfa_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idfa_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备idfa
       * </pre>
       *
       * <code>string idfa = 10;</code>
       * @param value The idfa to set.
       * @return This builder for chaining.
       */
      public Builder setIdfa(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        idfa_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备idfa
       * </pre>
       *
       * <code>string idfa = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdfa() {
        idfa_ = getDefaultInstance().getIdfa();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备idfa
       * </pre>
       *
       * <code>string idfa = 10;</code>
       * @param value The bytes for idfa to set.
       * @return This builder for chaining.
       */
      public Builder setIdfaBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        idfa_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object customInfo_ = "";
      /**
       * <pre>
       * 客户自定义信息，使用前需要线下约定。
       * </pre>
       *
       * <code>string custom_info = 11;</code>
       * @return The customInfo.
       */
      public java.lang.String getCustomInfo() {
        java.lang.Object ref = customInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          customInfo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 客户自定义信息，使用前需要线下约定。
       * </pre>
       *
       * <code>string custom_info = 11;</code>
       * @return The bytes for customInfo.
       */
      public com.google.protobuf.ByteString
          getCustomInfoBytes() {
        java.lang.Object ref = customInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          customInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 客户自定义信息，使用前需要线下约定。
       * </pre>
       *
       * <code>string custom_info = 11;</code>
       * @param value The customInfo to set.
       * @return This builder for chaining.
       */
      public Builder setCustomInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        customInfo_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户自定义信息，使用前需要线下约定。
       * </pre>
       *
       * <code>string custom_info = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearCustomInfo() {
        customInfo_ = getDefaultInstance().getCustomInfo();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户自定义信息，使用前需要线下约定。
       * </pre>
       *
       * <code>string custom_info = 11;</code>
       * @param value The bytes for customInfo to set.
       * @return This builder for chaining.
       */
      public Builder setCustomInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        customInfo_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object brand_ = "";
      /**
       * <pre>
       * 手机品牌
       * </pre>
       *
       * <code>string brand = 12;</code>
       * @return The brand.
       */
      public java.lang.String getBrand() {
        java.lang.Object ref = brand_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          brand_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 手机品牌
       * </pre>
       *
       * <code>string brand = 12;</code>
       * @return The bytes for brand.
       */
      public com.google.protobuf.ByteString
          getBrandBytes() {
        java.lang.Object ref = brand_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          brand_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 手机品牌
       * </pre>
       *
       * <code>string brand = 12;</code>
       * @param value The brand to set.
       * @return This builder for chaining.
       */
      public Builder setBrand(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        brand_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 手机品牌
       * </pre>
       *
       * <code>string brand = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearBrand() {
        brand_ = getDefaultInstance().getBrand();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 手机品牌
       * </pre>
       *
       * <code>string brand = 12;</code>
       * @param value The bytes for brand to set.
       * @return This builder for chaining.
       */
      public Builder setBrandBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        brand_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object channel_ = "";
      /**
       * <pre>
       * 渠道
       * </pre>
       *
       * <code>string channel = 13;</code>
       * @return The channel.
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 渠道
       * </pre>
       *
       * <code>string channel = 13;</code>
       * @return The bytes for channel.
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 渠道
       * </pre>
       *
       * <code>string channel = 13;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        channel_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 渠道
       * </pre>
       *
       * <code>string channel = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        channel_ = getDefaultInstance().getChannel();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 渠道
       * </pre>
       *
       * <code>string channel = 13;</code>
       * @param value The bytes for channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        channel_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.lang.Object sign_ = "";
      /**
       * <pre>
       * md5(id+request_time+channel+secretKey)
       * </pre>
       *
       * <code>string sign = 14;</code>
       * @return The sign.
       */
      public java.lang.String getSign() {
        java.lang.Object ref = sign_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sign_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * md5(id+request_time+channel+secretKey)
       * </pre>
       *
       * <code>string sign = 14;</code>
       * @return The bytes for sign.
       */
      public com.google.protobuf.ByteString
          getSignBytes() {
        java.lang.Object ref = sign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * md5(id+request_time+channel+secretKey)
       * </pre>
       *
       * <code>string sign = 14;</code>
       * @param value The sign to set.
       * @return This builder for chaining.
       */
      public Builder setSign(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        sign_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * md5(id+request_time+channel+secretKey)
       * </pre>
       *
       * <code>string sign = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSign() {
        sign_ = getDefaultInstance().getSign();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * md5(id+request_time+channel+secretKey)
       * </pre>
       *
       * <code>string sign = 14;</code>
       * @param value The bytes for sign to set.
       * @return This builder for chaining.
       */
      public Builder setSignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        sign_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.ximalaya.ad.rdp.proto.Req)
    }

    // @@protoc_insertion_point(class_scope:com.ximalaya.ad.rdp.proto.Req)
    private static final com.ximalaya.ad.rdp.proto.XimaRtaProto.Req DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.ximalaya.ad.rdp.proto.XimaRtaProto.Req();
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Req getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Req>
        PARSER = new com.google.protobuf.AbstractParser<Req>() {
      @java.lang.Override
      public Req parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Req> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Req> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.ximalaya.ad.rdp.proto.XimaRtaProto.Req getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RtaResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.ximalaya.ad.rdp.proto.RtaResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string rta_id = 1;</code>
     * @return The rtaId.
     */
    java.lang.String getRtaId();
    /**
     * <code>string rta_id = 1;</code>
     * @return The bytes for rtaId.
     */
    com.google.protobuf.ByteString
        getRtaIdBytes();

    /**
     * <pre>
     * 质量分
     * </pre>
     *
     * <code>double quality_score = 2;</code>
     * @return The qualityScore.
     */
    double getQualityScore();
  }
  /**
   * Protobuf type {@code com.ximalaya.ad.rdp.proto.RtaResult}
   */
  public static final class RtaResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.ximalaya.ad.rdp.proto.RtaResult)
      RtaResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RtaResult.newBuilder() to construct.
    private RtaResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RtaResult() {
      rtaId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RtaResult();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_RtaResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder.class);
    }

    public static final int RTA_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object rtaId_ = "";
    /**
     * <code>string rta_id = 1;</code>
     * @return The rtaId.
     */
    @java.lang.Override
    public java.lang.String getRtaId() {
      java.lang.Object ref = rtaId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rtaId_ = s;
        return s;
      }
    }
    /**
     * <code>string rta_id = 1;</code>
     * @return The bytes for rtaId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRtaIdBytes() {
      java.lang.Object ref = rtaId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rtaId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int QUALITY_SCORE_FIELD_NUMBER = 2;
    private double qualityScore_ = 0D;
    /**
     * <pre>
     * 质量分
     * </pre>
     *
     * <code>double quality_score = 2;</code>
     * @return The qualityScore.
     */
    @java.lang.Override
    public double getQualityScore() {
      return qualityScore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rtaId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, rtaId_);
      }
      if (java.lang.Double.doubleToRawLongBits(qualityScore_) != 0) {
        output.writeDouble(2, qualityScore_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(rtaId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, rtaId_);
      }
      if (java.lang.Double.doubleToRawLongBits(qualityScore_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(2, qualityScore_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult)) {
        return super.equals(obj);
      }
      com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult other = (com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult) obj;

      if (!getRtaId()
          .equals(other.getRtaId())) return false;
      if (java.lang.Double.doubleToLongBits(getQualityScore())
          != java.lang.Double.doubleToLongBits(
              other.getQualityScore())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RTA_ID_FIELD_NUMBER;
      hash = (53 * hash) + getRtaId().hashCode();
      hash = (37 * hash) + QUALITY_SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getQualityScore()));
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.ximalaya.ad.rdp.proto.RtaResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.ximalaya.ad.rdp.proto.RtaResult)
        com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_RtaResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder.class);
      }

      // Construct using com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rtaId_ = "";
        qualityScore_ = 0D;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getDefaultInstanceForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.getDefaultInstance();
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult build() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult buildPartial() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult result = new com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rtaId_ = rtaId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.qualityScore_ = qualityScore_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult) {
          return mergeFrom((com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult other) {
        if (other == com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.getDefaultInstance()) return this;
        if (!other.getRtaId().isEmpty()) {
          rtaId_ = other.rtaId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getQualityScore() != 0D) {
          setQualityScore(other.getQualityScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                rtaId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 17: {
                qualityScore_ = input.readDouble();
                bitField0_ |= 0x00000002;
                break;
              } // case 17
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object rtaId_ = "";
      /**
       * <code>string rta_id = 1;</code>
       * @return The rtaId.
       */
      public java.lang.String getRtaId() {
        java.lang.Object ref = rtaId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rtaId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string rta_id = 1;</code>
       * @return The bytes for rtaId.
       */
      public com.google.protobuf.ByteString
          getRtaIdBytes() {
        java.lang.Object ref = rtaId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rtaId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string rta_id = 1;</code>
       * @param value The rtaId to set.
       * @return This builder for chaining.
       */
      public Builder setRtaId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        rtaId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string rta_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRtaId() {
        rtaId_ = getDefaultInstance().getRtaId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string rta_id = 1;</code>
       * @param value The bytes for rtaId to set.
       * @return This builder for chaining.
       */
      public Builder setRtaIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        rtaId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private double qualityScore_ ;
      /**
       * <pre>
       * 质量分
       * </pre>
       *
       * <code>double quality_score = 2;</code>
       * @return The qualityScore.
       */
      @java.lang.Override
      public double getQualityScore() {
        return qualityScore_;
      }
      /**
       * <pre>
       * 质量分
       * </pre>
       *
       * <code>double quality_score = 2;</code>
       * @param value The qualityScore to set.
       * @return This builder for chaining.
       */
      public Builder setQualityScore(double value) {

        qualityScore_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 质量分
       * </pre>
       *
       * <code>double quality_score = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQualityScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        qualityScore_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.ximalaya.ad.rdp.proto.RtaResult)
    }

    // @@protoc_insertion_point(class_scope:com.ximalaya.ad.rdp.proto.RtaResult)
    private static final com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult();
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RtaResult>
        PARSER = new com.google.protobuf.AbstractParser<RtaResult>() {
      @java.lang.Override
      public RtaResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RtaResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RtaResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.ximalaya.ad.rdp.proto.Resp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <pre>
     * 响应码, 0为正常, 其他为异常
     * </pre>
     *
     * <code>int32 code = 2;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 信息, code=0时为空
     * </pre>
     *
     * <code>string message = 3;</code>
     * @return The message.
     */
    java.lang.String getMessage();
    /**
     * <pre>
     * 信息, code=0时为空
     * </pre>
     *
     * <code>string message = 3;</code>
     * @return The bytes for message.
     */
    com.google.protobuf.ByteString
        getMessageBytes();

    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
     * @return The enum numeric value on the wire for resultType.
     */
    int getResultTypeValue();
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
     * @return The resultType.
     */
    com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType getResultType();

    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> 
        getResultList();
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getResult(int index);
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    int getResultCount();
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    java.util.List<? extends com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder> 
        getResultOrBuilderList();
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder getResultOrBuilder(
        int index);

    /**
     * <pre>
     * result_type=ALL时的质量分
     * </pre>
     *
     * <code>double quality_score = 6;</code>
     * @return The qualityScore.
     */
    double getQualityScore();
  }
  /**
   * Protobuf type {@code com.ximalaya.ad.rdp.proto.Resp}
   */
  public static final class Resp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.ximalaya.ad.rdp.proto.Resp)
      RespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Resp.newBuilder() to construct.
    private Resp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Resp() {
      id_ = "";
      message_ = "";
      resultType_ = 0;
      result_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Resp();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Resp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.Builder.class);
    }

    /**
     * Protobuf enum {@code com.ximalaya.ad.rdp.proto.Resp.ResultType}
     */
    public enum ResultType
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <pre>
       * 全部都投
       * </pre>
       *
       * <code>ALL = 0;</code>
       */
      ALL(0),
      /**
       * <pre>
       * 全部不投
       * </pre>
       *
       * <code>NONE = 1;</code>
       */
      NONE(1),
      /**
       * <pre>
       * 只投一部分
       * </pre>
       *
       * <code>PART = 2;</code>
       */
      PART(2),
      UNRECOGNIZED(-1),
      ;

      /**
       * <pre>
       * 全部都投
       * </pre>
       *
       * <code>ALL = 0;</code>
       */
      public static final int ALL_VALUE = 0;
      /**
       * <pre>
       * 全部不投
       * </pre>
       *
       * <code>NONE = 1;</code>
       */
      public static final int NONE_VALUE = 1;
      /**
       * <pre>
       * 只投一部分
       * </pre>
       *
       * <code>PART = 2;</code>
       */
      public static final int PART_VALUE = 2;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ResultType valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static ResultType forNumber(int value) {
        switch (value) {
          case 0: return ALL;
          case 1: return NONE;
          case 2: return PART;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<ResultType>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          ResultType> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<ResultType>() {
              public ResultType findValueByNumber(int number) {
                return ResultType.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.getDescriptor().getEnumTypes().get(0);
      }

      private static final ResultType[] VALUES = values();

      public static ResultType valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private ResultType(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:com.ximalaya.ad.rdp.proto.Resp.ResultType)
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object id_ = "";
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CODE_FIELD_NUMBER = 2;
    private int code_ = 0;
    /**
     * <pre>
     * 响应码, 0为正常, 其他为异常
     * </pre>
     *
     * <code>int32 code = 2;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int MESSAGE_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object message_ = "";
    /**
     * <pre>
     * 信息, code=0时为空
     * </pre>
     *
     * <code>string message = 3;</code>
     * @return The message.
     */
    @java.lang.Override
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 信息, code=0时为空
     * </pre>
     *
     * <code>string message = 3;</code>
     * @return The bytes for message.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RESULT_TYPE_FIELD_NUMBER = 4;
    private int resultType_ = 0;
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
     * @return The enum numeric value on the wire for resultType.
     */
    @java.lang.Override public int getResultTypeValue() {
      return resultType_;
    }
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
     * @return The resultType.
     */
    @java.lang.Override public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType getResultType() {
      com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType result = com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.forNumber(resultType_);
      return result == null ? com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.UNRECOGNIZED : result;
    }

    public static final int RESULT_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> result_;
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> getResultList() {
      return result_;
    }
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder> 
        getResultOrBuilderList() {
      return result_;
    }
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    @java.lang.Override
    public int getResultCount() {
      return result_.size();
    }
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    @java.lang.Override
    public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getResult(int index) {
      return result_.get(index);
    }
    /**
     * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
     */
    @java.lang.Override
    public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder getResultOrBuilder(
        int index) {
      return result_.get(index);
    }

    public static final int QUALITY_SCORE_FIELD_NUMBER = 6;
    private double qualityScore_ = 0D;
    /**
     * <pre>
     * result_type=ALL时的质量分
     * </pre>
     *
     * <code>double quality_score = 6;</code>
     * @return The qualityScore.
     */
    @java.lang.Override
    public double getQualityScore() {
      return qualityScore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (code_ != 0) {
        output.writeInt32(2, code_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, message_);
      }
      if (resultType_ != com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.ALL.getNumber()) {
        output.writeEnum(4, resultType_);
      }
      for (int i = 0; i < result_.size(); i++) {
        output.writeMessage(5, result_.get(i));
      }
      if (java.lang.Double.doubleToRawLongBits(qualityScore_) != 0) {
        output.writeDouble(6, qualityScore_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, code_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, message_);
      }
      if (resultType_ != com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.ALL.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, resultType_);
      }
      for (int i = 0; i < result_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, result_.get(i));
      }
      if (java.lang.Double.doubleToRawLongBits(qualityScore_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, qualityScore_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp)) {
        return super.equals(obj);
      }
      com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp other = (com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (getCode()
          != other.getCode()) return false;
      if (!getMessage()
          .equals(other.getMessage())) return false;
      if (resultType_ != other.resultType_) return false;
      if (!getResultList()
          .equals(other.getResultList())) return false;
      if (java.lang.Double.doubleToLongBits(getQualityScore())
          != java.lang.Double.doubleToLongBits(
              other.getQualityScore())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
      hash = (53 * hash) + getMessage().hashCode();
      hash = (37 * hash) + RESULT_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + resultType_;
      if (getResultCount() > 0) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResultList().hashCode();
      }
      hash = (37 * hash) + QUALITY_SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getQualityScore()));
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.ximalaya.ad.rdp.proto.Resp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.ximalaya.ad.rdp.proto.Resp)
        com.ximalaya.ad.rdp.proto.XimaRtaProto.RespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Resp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.class, com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.Builder.class);
      }

      // Construct using com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        code_ = 0;
        message_ = "";
        resultType_ = 0;
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
        } else {
          result_ = null;
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        qualityScore_ = 0D;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp getDefaultInstanceForType() {
        return com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.getDefaultInstance();
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp build() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp buildPartial() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp result = new com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp result) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            result_ = java.util.Collections.unmodifiableList(result_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.result_ = result_;
        } else {
          result.result_ = resultBuilder_.build();
        }
      }

      private void buildPartial0(com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.message_ = message_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.resultType_ = resultType_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.qualityScore_ = qualityScore_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp) {
          return mergeFrom((com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp other) {
        if (other == com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.getMessage().isEmpty()) {
          message_ = other.message_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.resultType_ != 0) {
          setResultTypeValue(other.getResultTypeValue());
        }
        if (resultBuilder_ == null) {
          if (!other.result_.isEmpty()) {
            if (result_.isEmpty()) {
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureResultIsMutable();
              result_.addAll(other.result_);
            }
            onChanged();
          }
        } else {
          if (!other.result_.isEmpty()) {
            if (resultBuilder_.isEmpty()) {
              resultBuilder_.dispose();
              resultBuilder_ = null;
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000010);
              resultBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResultFieldBuilder() : null;
            } else {
              resultBuilder_.addAllMessages(other.result_);
            }
          }
        }
        if (other.getQualityScore() != 0D) {
          setQualityScore(other.getQualityScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                message_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                resultType_ = input.readEnum();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult m =
                    input.readMessage(
                        com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.parser(),
                        extensionRegistry);
                if (resultBuilder_ == null) {
                  ensureResultIsMutable();
                  result_.add(m);
                } else {
                  resultBuilder_.addMessage(m);
                }
                break;
              } // case 42
              case 49: {
                qualityScore_ = input.readDouble();
                bitField0_ |= 0x00000020;
                break;
              } // case 49
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object id_ = "";
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The id.
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求唯一id
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int code_ ;
      /**
       * <pre>
       * 响应码, 0为正常, 其他为异常
       * </pre>
       *
       * <code>int32 code = 2;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <pre>
       * 响应码, 0为正常, 其他为异常
       * </pre>
       *
       * <code>int32 code = 2;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 响应码, 0为正常, 其他为异常
       * </pre>
       *
       * <code>int32 code = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        code_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object message_ = "";
      /**
       * <pre>
       * 信息, code=0时为空
       * </pre>
       *
       * <code>string message = 3;</code>
       * @return The message.
       */
      public java.lang.String getMessage() {
        java.lang.Object ref = message_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          message_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 信息, code=0时为空
       * </pre>
       *
       * <code>string message = 3;</code>
       * @return The bytes for message.
       */
      public com.google.protobuf.ByteString
          getMessageBytes() {
        java.lang.Object ref = message_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          message_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 信息, code=0时为空
       * </pre>
       *
       * <code>string message = 3;</code>
       * @param value The message to set.
       * @return This builder for chaining.
       */
      public Builder setMessage(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        message_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 信息, code=0时为空
       * </pre>
       *
       * <code>string message = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessage() {
        message_ = getDefaultInstance().getMessage();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 信息, code=0时为空
       * </pre>
       *
       * <code>string message = 3;</code>
       * @param value The bytes for message to set.
       * @return This builder for chaining.
       */
      public Builder setMessageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        message_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private int resultType_ = 0;
      /**
       * <pre>
       * 结果类型, 等于PART时, result必填
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
       * @return The enum numeric value on the wire for resultType.
       */
      @java.lang.Override public int getResultTypeValue() {
        return resultType_;
      }
      /**
       * <pre>
       * 结果类型, 等于PART时, result必填
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
       * @param value The enum numeric value on the wire for resultType to set.
       * @return This builder for chaining.
       */
      public Builder setResultTypeValue(int value) {
        resultType_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 结果类型, 等于PART时, result必填
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
       * @return The resultType.
       */
      @java.lang.Override
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType getResultType() {
        com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType result = com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.forNumber(resultType_);
        return result == null ? com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 结果类型, 等于PART时, result必填
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
       * @param value The resultType to set.
       * @return This builder for chaining.
       */
      public Builder setResultType(com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp.ResultType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        resultType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 结果类型, 等于PART时, result必填
       * </pre>
       *
       * <code>.com.ximalaya.ad.rdp.proto.Resp.ResultType result_type = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearResultType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        resultType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> result_ =
        java.util.Collections.emptyList();
      private void ensureResultIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          result_ = new java.util.ArrayList<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult>(result_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder> resultBuilder_;

      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> getResultList() {
        if (resultBuilder_ == null) {
          return java.util.Collections.unmodifiableList(result_);
        } else {
          return resultBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public int getResultCount() {
        if (resultBuilder_ == null) {
          return result_.size();
        } else {
          return resultBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult getResult(int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);
        } else {
          return resultBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder setResult(
          int index, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.set(index, value);
          onChanged();
        } else {
          resultBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder setResult(
          int index, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.set(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder addResult(com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(value);
          onChanged();
        } else {
          resultBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder addResult(
          int index, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(index, value);
          onChanged();
        } else {
          resultBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder addResult(
          com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder addResult(
          int index, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder addAllResult(
          java.lang.Iterable<? extends com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult> values) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, result_);
          onChanged();
        } else {
          resultBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public Builder removeResult(int index) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.remove(index);
          onChanged();
        } else {
          resultBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder getResultBuilder(
          int index) {
        return getResultFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder getResultOrBuilder(
          int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);  } else {
          return resultBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public java.util.List<? extends com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder> 
           getResultOrBuilderList() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(result_);
        }
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder addResultBuilder() {
        return getResultFieldBuilder().addBuilder(
            com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.getDefaultInstance());
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder addResultBuilder(
          int index) {
        return getResultFieldBuilder().addBuilder(
            index, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.getDefaultInstance());
      }
      /**
       * <code>repeated .com.ximalaya.ad.rdp.proto.RtaResult result = 5;</code>
       */
      public java.util.List<com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder> 
           getResultBuilderList() {
        return getResultFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResult.Builder, com.ximalaya.ad.rdp.proto.XimaRtaProto.RtaResultOrBuilder>(
                  result_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }

      private double qualityScore_ ;
      /**
       * <pre>
       * result_type=ALL时的质量分
       * </pre>
       *
       * <code>double quality_score = 6;</code>
       * @return The qualityScore.
       */
      @java.lang.Override
      public double getQualityScore() {
        return qualityScore_;
      }
      /**
       * <pre>
       * result_type=ALL时的质量分
       * </pre>
       *
       * <code>double quality_score = 6;</code>
       * @param value The qualityScore to set.
       * @return This builder for chaining.
       */
      public Builder setQualityScore(double value) {

        qualityScore_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * result_type=ALL时的质量分
       * </pre>
       *
       * <code>double quality_score = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearQualityScore() {
        bitField0_ = (bitField0_ & ~0x00000020);
        qualityScore_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.ximalaya.ad.rdp.proto.Resp)
    }

    // @@protoc_insertion_point(class_scope:com.ximalaya.ad.rdp.proto.Resp)
    private static final com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp();
    }

    public static com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Resp>
        PARSER = new com.google.protobuf.AbstractParser<Resp>() {
      @java.lang.Override
      public Resp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Resp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Resp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.ximalaya.ad.rdp.proto.XimaRtaProto.Resp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_ximalaya_ad_rdp_proto_Req_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_ximalaya_ad_rdp_proto_RtaResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_ximalaya_ad_rdp_proto_Resp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022ximalaya-rta.proto\022\031com.ximalaya.ad.rd" +
      "p.proto\"\217\002\n\003Req\022\n\n\002id\030\001 \001(\t\022\024\n\014request_t" +
      "ime\030\002 \001(\003\022\017\n\007rta_ids\030\003 \003(\t\0222\n\007os_type\030\004 " +
      "\001(\0162!.com.ximalaya.ad.rdp.proto.OsType\022\020" +
      "\n\010imei_md5\030\005 \001(\t\022\014\n\004imei\030\006 \001(\t\022\020\n\010oaid_m" +
      "d5\030\007 \001(\t\022\014\n\004oaid\030\010 \001(\t\022\020\n\010idfa_md5\030\t \001(\t" +
      "\022\014\n\004idfa\030\n \001(\t\022\023\n\013custom_info\030\013 \001(\t\022\r\n\005b" +
      "rand\030\014 \001(\t\022\017\n\007channel\030\r \001(\t\022\014\n\004sign\030\016 \001(" +
      "\t\"2\n\tRtaResult\022\016\n\006rta_id\030\001 \001(\t\022\025\n\rqualit" +
      "y_score\030\002 \001(\001\"\352\001\n\004Resp\022\n\n\002id\030\001 \001(\t\022\014\n\004co" +
      "de\030\002 \001(\005\022\017\n\007message\030\003 \001(\t\022?\n\013result_type" +
      "\030\004 \001(\0162*.com.ximalaya.ad.rdp.proto.Resp." +
      "ResultType\0224\n\006result\030\005 \003(\0132$.com.ximalay" +
      "a.ad.rdp.proto.RtaResult\022\025\n\rquality_scor" +
      "e\030\006 \001(\001\")\n\nResultType\022\007\n\003ALL\020\000\022\010\n\004NONE\020\001" +
      "\022\010\n\004PART\020\002*.\n\006OsType\022\016\n\nUNKNOWN_OS\020\000\022\013\n\007" +
      "ANDROID\020\001\022\007\n\003IOS\020\002B)\n\031com.ximalaya.ad.rd" +
      "p.protoB\014XimaRtaProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_ximalaya_ad_rdp_proto_Req_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_ximalaya_ad_rdp_proto_Req_descriptor,
        new java.lang.String[] { "Id", "RequestTime", "RtaIds", "OsType", "ImeiMd5", "Imei", "OaidMd5", "Oaid", "IdfaMd5", "Idfa", "CustomInfo", "Brand", "Channel", "Sign", });
    internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_ximalaya_ad_rdp_proto_RtaResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_ximalaya_ad_rdp_proto_RtaResult_descriptor,
        new java.lang.String[] { "RtaId", "QualityScore", });
    internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_ximalaya_ad_rdp_proto_Resp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_ximalaya_ad_rdp_proto_Resp_descriptor,
        new java.lang.String[] { "Id", "Code", "Message", "ResultType", "Result", "QualityScore", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
